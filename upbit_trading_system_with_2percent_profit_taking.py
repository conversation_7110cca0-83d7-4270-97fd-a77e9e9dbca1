"""
업비트 통합 트레이딩 시스템 - 개별 수익률 2% 달성 시 50% 익절 기능 포함
"""
import pandas as pd
import numpy as np
import pyupbit
import time
import os
from datetime import datetime
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

# 환경변수 로드
load_dotenv()


class UpbitTradingSystemWith2PercentProfitTaking:
    """업비트 트레이딩 시스템 - 개별 수익률 2% 달성 시 50% 익절"""
    
    def __init__(self, live_trading: bool = False):
        """
        Args:
            live_trading: 실제 거래 여부 (False=시뮬레이션)
        """
        self.live_trading = live_trading
        
        # 업비트 API 초기화
        if live_trading:
            access_key = os.getenv("UPBIT_ACCESS_KEY")
            secret_key = os.getenv("UPBIT_SECRET_KEY")
            if not access_key or not secret_key:
                print("❌ 업비트 API 키가 설정되지 않았습니다!")
                print("💡 .env 파일에 다음과 같이 설정하세요:")
                print("   UPBIT_ACCESS_KEY=your_access_key")
                print("   UPBIT_SECRET_KEY=your_secret_key")
                print("🔄 시뮬레이션 모드로 전환합니다.")
                self.live_trading = False
                self.upbit = None
            else:
                try:
                    self.upbit = pyupbit.Upbit(access_key, secret_key)
                    print("✅ 업비트 API 연결 성공")
                except Exception as e:
                    print(f"❌ 업비트 API 연결 실패: {e}")
                    print("🔄 시뮬레이션 모드로 전환합니다.")
                    self.live_trading = False
                    self.upbit = None
        else:
            self.upbit = None
            print("⚠️  시뮬레이션 모드 - 실제 거래하지 않습니다")
        
        # 거래 설정
        self.symbols = ["KRW-BTC", "KRW-ETH", "KRW-SOL", "KRW-XRP"]
        self.timeframe = "minute5"
        self.max_investment_per_coin = 100000  # 종목당 최대 10만원
        self.order_amount = 15000  # 1회 주문 금액
        self.min_order_amount = 5000  # 최소 주문 금액
        
        # 상태 추적
        self.last_signals = {}
        self.buy_prices = {}  # 매수 가격 추적
        self.profit_taken_symbols = set()  # 2% 익절 완료된 심볼 추적
        
        print(f"🚀 업비트 트레이딩 시스템 초기화 완료")
        print(f"   - 실제 거래: {'ON' if live_trading else 'OFF'}")
        print(f"   - 대상 종목: {len(self.symbols)}개")
        print(f"   - 개별 수익률 2% 달성 시 50% 익절 기능 활성화")
    
    def get_account_info(self) -> dict:
        """계좌 정보 조회"""
        if not self.live_trading or not self.upbit:
            return {'krw_balance': 1000000, 'positions': {}}  # 시뮬레이션
        
        try:
            # KRW 잔고
            krw_balance = self.upbit.get_balance("KRW")
            
            # 보유 코인 잔고
            positions = {}
            for symbol in self.symbols:
                coin_name = symbol.split("-")[1]
                balance = self.upbit.get_balance(coin_name)
                if balance > 0:
                    current_price = pyupbit.get_current_price(symbol)
                    positions[symbol] = {
                        'balance': balance,
                        'current_price': current_price,
                        'value': balance * current_price
                    }
            
            return {
                'krw_balance': krw_balance,
                'positions': positions,
                'total_value': krw_balance + sum(pos['value'] for pos in positions.values())
            }
            
        except Exception as e:
            print(f"❌ 계좌 정보 조회 오류: {e}")
            return {'error': str(e)}
    
    def check_individual_profit_2percent(self, symbol: str) -> dict:
        """개별 코인 수익률 2% 달성 시 50% 익절 조건 확인"""
        if not self.live_trading or symbol not in self.buy_prices:
            return {'should_sell': False, 'reason': 'no_position_or_simulation'}

        try:
            current_price = pyupbit.get_current_price(symbol)
            buy_price = self.buy_prices[symbol]

            # 개별 수익률 계산
            profit_rate = (current_price - buy_price) / buy_price * 100

            # 개별 수익률 2% 달성 시 50% 익절
            if profit_rate >= 2.0:
                coin_name = symbol.split("-")[1]
                coin_balance = self.upbit.get_balance(coin_name)

                if coin_balance > 0:
                    sell_amount = coin_balance * 0.5  # 50% 익절
                    return {
                        'should_sell': True,
                        'sell_amount': sell_amount,
                        'profit_rate': profit_rate,
                        'reason': f'개별수익률 2% 달성 - 50% 익절 (수익률: {profit_rate:.2f}%)'
                    }

            return {'should_sell': False, 'profit_rate': profit_rate}

        except Exception as e:
            print(f"❌ {symbol} 익절 조건 확인 오류: {e}")
            return {'should_sell': False, 'reason': 'error'}

    def monitor_all_positions_for_2percent_profit_taking(self):
        """🎯 활성된 모든 포지션의 개별 수익률 2% 달성 시 50% 익절 모니터링"""
        if not self.live_trading:
            print("📝 [시뮬레이션] 포지션 모니터링 스킵")
            return
        
        try:
            account = self.get_account_info()
            if 'error' in account or not account.get('positions'):
                print("📊 현재 활성 포지션 없음")
                return
            
            print(f"\n🎯 활성 포지션 개별 수익률 2% 익절 모니터링")
            
            for symbol, pos in account['positions'].items():
                if symbol in self.buy_prices and symbol not in self.profit_taken_symbols:
                    current_price = pyupbit.get_current_price(symbol)
                    buy_price = self.buy_prices[symbol]
                    profit_rate = (current_price - buy_price) / buy_price * 100
                    
                    print(f"   📊 {symbol}: 현재가 {current_price:,.0f}원 | 매수가 {buy_price:,.0f}원 | 수익률 {profit_rate:+.2f}%")
                    
                    # 🚨 개별 수익률 2% 달성 시 50% 익절 체크 (중복 방지)
                    if profit_rate >= 2.0:
                        coin_name = symbol.split("-")[1]
                        coin_balance = self.upbit.get_balance(coin_name)
                        
                        if coin_balance > 0:
                            sell_amount = coin_balance * 0.5  # 50% 익절
                            
                            print(f"   🚨 {symbol} 개별 수익률 2% 달성! (수익률: {profit_rate:.2f}%)")
                            print(f"   💰 50% 익절 실행: {sell_amount:.6f}개")
                            
                            success = self.execute_50percent_profit_taking(symbol, sell_amount, profit_rate)
                            
                            if success:
                                print(f"   ✅ {symbol} 50% 익절 완료! 남은 50%로 추가 수익 추구")
                                # 중복 익절 방지를 위해 추적 목록에 추가
                                self.profit_taken_symbols.add(symbol)
                                print(f"   📝 {symbol} 2% 익절 완료 기록 - 중복 익절 방지")
                            else:
                                print(f"   ❌ {symbol} 익절 실패")
                
                elif symbol in self.profit_taken_symbols:
                    # 이미 2% 익절한 심볼의 현재 상태 표시
                    if symbol in self.buy_prices:
                        current_price = pyupbit.get_current_price(symbol)
                        buy_price = self.buy_prices[symbol]
                        profit_rate = (current_price - buy_price) / buy_price * 100
                        print(f"   📈 {symbol}: 수익률 {profit_rate:+.2f}% (이미 2% 익절 완료, 남은 50% 보유 중)")
                            
        except Exception as e:
            print(f"❌ 포지션 모니터링 오류: {e}")

    def execute_50percent_profit_taking(self, symbol: str, sell_amount: float, profit_rate: float) -> bool:
        """50% 익절 실행"""
        try:
            result = self.upbit.sell_market_order(symbol, sell_amount)
            if result:
                print(f"💰 50% 익절 완료: {symbol} {sell_amount:.8f}개 (수익률: {profit_rate:.2f}%)")
                
                # 익절 로그 저장
                with open('profit_taking_log.txt', 'a', encoding='utf-8') as f:
                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    f.write(f"{timestamp} - {symbol} 2% 익절: "
                           f"수량 {sell_amount:.8f}개, 수익률 {profit_rate:.2f}%\n")
                
                return True
            else:
                print(f"❌ 익절 실패: {symbol}")
                return False
        except Exception as e:
            print(f"❌ {symbol} 익절 실행 오류: {e}")
            return False

    def run_monitoring_cycle(self):
        """모니터링 사이클 실행"""
        print(f"\n{'='*60}")
        print(f"🔍 포지션 모니터링: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")

        # 🎯 우선순위 1: 활성된 모든 포지션의 개별 수익률 2% 달성 시 50% 익절 모니터링
        self.monitor_all_positions_for_2percent_profit_taking()

        # 계좌 정보 출력
        account = self.get_account_info()
        if 'error' not in account:
            print(f"\n💰 KRW 잔고: {account['krw_balance']:,.0f}원")
            if account['positions']:
                print("📊 보유 포지션:")
                for symbol, pos in account['positions'].items():
                    print(f"   {symbol}: {pos['balance']:.6f}개 ({pos['value']:,.0f}원)")
                    
                    # 수익률 정보 표시
                    if symbol in self.buy_prices:
                        current_price = pyupbit.get_current_price(symbol)
                        buy_price = self.buy_prices[symbol]
                        profit_rate = (current_price - buy_price) / buy_price * 100
                        status = "이미 2% 익절 완료" if symbol in self.profit_taken_symbols else "익절 대기 중"
                        print(f"   💹 수익률: {profit_rate:+.2f}% (매수가: {buy_price:,.0f}원) - {status}")
            else:
                print("📊 현재 보유 포지션 없음")

    def run_continuous_monitoring(self, interval_minutes: int = 1):
        """연속 모니터링"""
        print(f"🔄 연속 포지션 모니터링 시작 (간격: {interval_minutes}분)")
        print("📊 개별 수익률 2% 달성 시 자동으로 50% 익절 실행")
        print("Ctrl+C로 중지할 수 있습니다.")
        
        try:
            while True:
                self.run_monitoring_cycle()
                
                print(f"\n⏰ {interval_minutes}분 대기 중...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 사용자에 의해 중지되었습니다.")
        except Exception as e:
            print(f"\n❌ 시스템 오류: {e}")

    def add_test_position(self, symbol: str, buy_price: float):
        """테스트용 포지션 추가"""
        self.buy_prices[symbol] = buy_price
        print(f"📝 테스트 포지션 추가: {symbol} 매수가 {buy_price:,.0f}원")


def main():
    """메인 실행 함수"""
    print("🚀 업비트 개별 수익률 2% 익절 시스템")
    print("=" * 50)
    print("💰 각 코인의 개별 수익률이 2% 달성 시 자동으로 50% 익절")
    print("🎯 남은 50% 물량으로 추가 수익 추구")
    
    # 실행 모드 선택
    print("\n실행 모드를 선택하세요:")
    print("1. 시뮬레이션 모드 (안전)")
    print("2. 실제 거래 모드 (주의!)")
    
    try:
        choice = input("선택 (1 또는 2): ").strip()
        
        if choice == "1":
            live_trading = False
            print("✅ 시뮬레이션 모드 선택")
        elif choice == "2":
            live_trading = True
            confirm = input("⚠️  실제 거래 모드입니다. 정말 진행하시겠습니까? (yes/no): ").strip().lower()
            if confirm != "yes":
                print("❌ 취소되었습니다.")
                return
            print("⚠️  실제 거래 모드 선택")
        else:
            print("❌ 잘못된 선택입니다.")
            return
        
        # 시스템 초기화
        system = UpbitTradingSystemWith2PercentProfitTaking(live_trading=live_trading)
        
        # 실행 방식 선택
        print("\n실행 방식을 선택하세요:")
        print("1. 단일 모니터링")
        print("2. 연속 모니터링 (1분 간격)")
        print("3. 테스트 포지션 추가")
        
        run_choice = input("선택 (1, 2, 또는 3): ").strip()
        
        if run_choice == "1":
            system.run_monitoring_cycle()
        elif run_choice == "2":
            system.run_continuous_monitoring(1)
        elif run_choice == "3":
            # 테스트 포지션 추가
            symbol = input("테스트 심볼 입력 (예: KRW-BTC): ").strip()
            buy_price = float(input("매수가 입력 (예: 50000000): ").strip())
            system.add_test_position(symbol, buy_price)
            system.run_monitoring_cycle()
        else:
            print("❌ 잘못된 선택입니다.")
            
    except Exception as e:
        print(f"❌ 시스템 오류: {e}")


if __name__ == "__main__":
    main()
