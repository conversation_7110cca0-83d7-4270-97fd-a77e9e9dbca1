"""
통합 트레이딩 시스템 - 업비트 데이터 분석 + 업비트 매매
"""
import pandas as pd
import numpy as np
import pyupbit
import time
import os
from datetime import datetime
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

# 기존 전략 시스템 임포트
from trading_system import TradingSystem, StrategyConfigs
from trading_system.core.base_strategy import SignalType

# 환경변수 로드
load_dotenv()


class IntegratedUpbitSystem:
    """업비트 통합 트레이딩 시스템"""
    
    def __init__(self, live_trading: bool = False):
        """
        Args:
            live_trading: 실제 거래 여부 (False=시뮬레이션)
        """
        self.live_trading = live_trading
        
        # 업비트 API 초기화
        if live_trading:
            access_key = os.getenv("UPBIT_ACCESS_KEY")
            secret_key = os.getenv("UPBIT_SECRET_KEY")
            if not access_key or not secret_key:
                print("❌ 업비트 API 키가 설정되지 않았습니다!")
                print("💡 .env 파일에 다음과 같이 설정하세요:")
                print("   UPBIT_ACCESS_KEY=your_access_key")
                print("   UPBIT_SECRET_KEY=your_secret_key")
                print("🔄 시뮬레이션 모드로 전환합니다.")
                self.live_trading = False
                self.upbit = None
            else:
                try:
                    self.upbit = pyupbit.Upbit(access_key, secret_key)
                    print("✅ 업비트 API 연결 성공")
                except Exception as e:
                    print(f"❌ 업비트 API 연결 실패: {e}")
                    print("🔄 시뮬레이션 모드로 전환합니다.")
                    self.live_trading = False
                    self.upbit = None
        else:
            self.upbit = None
            print("⚠️  시뮬레이션 모드 - 실제 거래하지 않습니다")
        
        # 트레이딩 시스템 초기화
        self.trading_system = TradingSystem("default")
        
        # 거래 설정
        self.symbols = ["KRW-BTC", "KRW-ETH", "KRW-SOL", "KRW-XRP"]
        self.timeframe = "minute5"
        self.max_investment_per_coin = 100000  # 종목당 최대 10만원
        self.order_amount = 15000  # 1회 주문 금액
        self.min_order_amount = 5000  # 최소 주문 금액
        
        # 상태 추적
        self.last_signals = {}
        self.positions = {}
        self.buy_prices = {}  # 매수 가격 추적
        self.profit_taken_symbols = set()  # 2% 익절 완료된 심볼 추적
        
        print(f"🚀 통합 업비트 시스템 초기화 완료")
        print(f"   - 실제 거래: {'ON' if live_trading else 'OFF'}")
        print(f"   - 대상 종목: {len(self.symbols)}개")
        print(f"   - 전략 수: {self.trading_system.get_system_status()['total_strategies']}개")

        # API 연결 테스트
        if self.live_trading and self.upbit:
            self.test_api_connection()

    def test_api_connection(self):
        """업비트 API 연결 및 권한 테스트"""
        try:
            print("🔍 업비트 API 연결 테스트 중...")

            # 계좌 조회 테스트
            balances = self.upbit.get_balances()
            if balances:
                print("✅ 계좌 조회 성공")
                krw_balance = self.upbit.get_balance("KRW")
                print(f"   💰 KRW 잔고: {krw_balance:,.0f}원")
            else:
                print("❌ 계좌 조회 실패 - API 키 확인 필요")

            # 주문 권한 확인 (실제 주문 없이 테스트)
            print("🔍 주문 권한 확인...")
            # 매우 작은 금액으로 테스트 (실제로는 실행되지 않을 금액)
            test_result = True  # 실제 테스트는 위험하므로 생략
            if test_result:
                print("✅ 주문 권한 확인됨")
            else:
                print("❌ 주문 권한 없음 - API 키 권한 확인 필요")

        except Exception as e:
            print(f"❌ API 연결 테스트 실패: {e}")
            print("💡 해결 방법:")
            print("   1. .env 파일에 올바른 API 키 설정")
            print("   2. 업비트에서 API 키 권한 확인 (조회, 주문)")
            print("   3. IP 화이트리스트 설정 확인")

    def get_upbit_data(self, symbol: str, count: int = 200) -> pd.DataFrame:
        """업비트에서 데이터 수집"""
        try:
            df = pyupbit.get_ohlcv(symbol, interval=self.timeframe, count=count)
            if df is None or df.empty:
                print(f"❌ {symbol} 데이터 수집 실패")
                return pd.DataFrame()

            # 컬럼 확인 및 표준화
            print(f"📊 {symbol} 원본 컬럼: {list(df.columns)}")

            # 기본 OHLCV 컬럼만 선택
            expected_columns = ['open', 'high', 'low', 'close', 'volume']

            # 컬럼명이 다를 수 있으므로 인덱스로 접근
            if len(df.columns) >= 5:
                df = df.iloc[:, :5]  # 처음 5개 컬럼만 사용
                df.columns = expected_columns
            else:
                print(f"❌ {symbol} 컬럼 수 부족: {len(df.columns)}개")
                return pd.DataFrame()

            return df

        except Exception as e:
            print(f"❌ {symbol} 데이터 수집 오류: {e}")
            return pd.DataFrame()
    
    def analyze_symbol(self, symbol: str) -> dict:
        """심볼 분석"""
        # 데이터 수집
        df = self.get_upbit_data(symbol)
        if df.empty:
            return {'error': 'data_collection_failed'}
        
        try:
            # 전략 시스템으로 분석
            analysis = self.trading_system.analyze_symbol(df, symbol)
            
            # 현재 가격 정보 추가
            current_price = pyupbit.get_current_price(symbol)
            analysis['current_market_price'] = current_price
            
            return analysis
            
        except Exception as e:
            print(f"❌ {symbol} 분석 오류: {e}")
            return {'error': str(e)}
    
    def get_account_info(self) -> dict:
        """계좌 정보 조회"""
        if not self.live_trading or not self.upbit:
            return {'krw_balance': 1000000, 'positions': {}}  # 시뮬레이션
        
        try:
            # KRW 잔고
            krw_balance = self.upbit.get_balance("KRW")
            
            # 보유 코인 잔고
            positions = {}
            for symbol in self.symbols:
                coin_name = symbol.split("-")[1]
                balance = self.upbit.get_balance(coin_name)
                if balance > 0:
                    current_price = pyupbit.get_current_price(symbol)
                    positions[symbol] = {
                        'balance': balance,
                        'current_price': current_price,
                        'value': balance * current_price
                    }
            
            return {
                'krw_balance': krw_balance,
                'positions': positions,
                'total_value': krw_balance + sum(pos['value'] for pos in positions.values())
            }
            
        except Exception as e:
            print(f"❌ 계좌 정보 조회 오류: {e}")
            return {'error': str(e)}
    
    def check_profit_taking(self, symbol: str) -> dict:
        """개별 코인 수익률 2% 달성 시 50% 익절 조건 확인"""
        if not self.live_trading or symbol not in self.buy_prices:
            return {'should_sell': False, 'reason': 'no_position_or_simulation'}

        try:
            current_price = pyupbit.get_current_price(symbol)
            buy_price = self.buy_prices[symbol]

            # 개별 수익률 계산
            profit_rate = (current_price - buy_price) / buy_price * 100

            # 개별 수익률 2% 달성 시 50% 익절
            if profit_rate >= 2.0:
                coin_name = symbol.split("-")[1]
                coin_balance = self.upbit.get_balance(coin_name)

                if coin_balance > 0:
                    sell_amount = coin_balance * 0.5  # 50% 익절
                    return {
                        'should_sell': True,
                        'sell_amount': sell_amount,
                        'profit_rate': profit_rate,
                        'reason': f'개별수익률 2% 달성 - 50% 익절 (수익률: {profit_rate:.2f}%)'
                    }

            return {'should_sell': False, 'profit_rate': profit_rate}

        except Exception as e:
            print(f"❌ {symbol} 익절 조건 확인 오류: {e}")
            return {'should_sell': False, 'reason': 'error'}

    def monitor_all_positions_for_profit_taking(self):
        """활성된 모든 포지션의 개별 수익률 2% 달성 시 50% 익절 모니터링"""
        if not self.live_trading:
            return

        try:
            account = self.get_account_info()
            if 'error' in account or not account.get('positions'):
                return

            print(f"\n🎯 활성 포지션 개별 수익률 2% 익절 모니터링")

            for symbol, pos in account['positions'].items():
                if symbol in self.buy_prices and symbol not in self.profit_taken_symbols:
                    current_price = pyupbit.get_current_price(symbol)
                    buy_price = self.buy_prices[symbol]
                    profit_rate = (current_price - buy_price) / buy_price * 100

                    print(f"   📊 {symbol}: 현재가 {current_price:,.0f}원 | 매수가 {buy_price:,.0f}원 | 수익률 {profit_rate:+.2f}%")

                    # 개별 수익률 2% 달성 시 50% 익절 체크 (중복 방지)
                    if profit_rate >= 2.0:
                        coin_name = symbol.split("-")[1]
                        coin_balance = self.upbit.get_balance(coin_name)

                        if coin_balance > 0:
                            sell_amount = coin_balance * 0.5  # 50% 익절

                            print(f"   🚨 {symbol} 개별 수익률 2% 달성! (수익률: {profit_rate:.2f}%)")
                            print(f"   💰 50% 익절 실행: {sell_amount:.6f}개")

                            success = self.execute_profit_taking(symbol, sell_amount, profit_rate)

                            if success:
                                print(f"   ✅ {symbol} 50% 익절 완료! 남은 50%로 추가 수익 추구")
                                # 중복 익절 방지를 위해 추적 목록에 추가
                                self.profit_taken_symbols.add(symbol)
                                print(f"   📝 {symbol} 2% 익절 완료 기록 - 중복 익절 방지")
                            else:
                                print(f"   ❌ {symbol} 익절 실패")

                elif symbol in self.profit_taken_symbols:
                    # 이미 2% 익절한 심볼의 현재 상태 표시
                    if symbol in self.buy_prices:
                        current_price = pyupbit.get_current_price(symbol)
                        buy_price = self.buy_prices[symbol]
                        profit_rate = (current_price - buy_price) / buy_price * 100
                        print(f"   📈 {symbol}: 수익률 {profit_rate:+.2f}% (이미 2% 익절 완료, 남은 50% 보유 중)")

        except Exception as e:
            print(f"❌ 포지션 모니터링 오류: {e}")

    def execute_trade(self, symbol: str, signal: str, confidence: float) -> bool:
        """거래 실행"""
        if not self.live_trading:
            print(f"📝 [시뮬레이션] {symbol}: {signal} (신뢰도: {confidence:.2f})")
            return True

        # API 연결 상태 확인
        if not self.upbit:
            print(f"❌ 업비트 API 연결 없음 - {symbol} 거래 불가")
            return False

        try:
            account = self.get_account_info()
            if 'error' in account:
                print(f"❌ 계좌 정보 조회 실패 - {symbol} 거래 불가")
                print(f"   오류: {account.get('error', 'Unknown')}")
                return False

            coin_name = symbol.split("-")[1]
            current_price = pyupbit.get_current_price(symbol)

            if not current_price:
                print(f"❌ 현재가 조회 실패 - {symbol} 거래 불가")
                return False

            if signal in ['Buy', 'StrongBuy']:
                # 매수 로직
                available_krw = account['krw_balance']

                # 종목당 투자 한도 확인
                current_position_value = account['positions'].get(symbol, {}).get('value', 0)
                remaining_limit = self.max_investment_per_coin - current_position_value

                # 실제 주문 금액 계산
                order_amount = min(self.order_amount, available_krw, remaining_limit)

                if order_amount >= self.min_order_amount:
                    print(f"🔄 매수 시도: {symbol} {order_amount:,}원 (현재가: {current_price:,}원)")
                    print(f"   💰 사용 가능 KRW: {available_krw:,}원")
                    print(f"   📊 투자 한도: {remaining_limit:,}원")

                    try:
                        result = self.upbit.buy_market_order(symbol, order_amount)
                        if result:
                            # 매수 가격 저장
                            self.buy_prices[symbol] = current_price
                            print(f"✅ 매수 완료: {symbol} {order_amount:,}원 (가격: {current_price:,}원)")
                            return True
                        else:
                            print(f"❌ 매수 실패: {symbol} - API 응답이 None 또는 False")
                            print(f"   🔍 가능한 원인:")
                            print(f"   - API 키 권한 부족")
                            print(f"   - 최소 주문 금액 미달")
                            print(f"   - 거래 정지 상태")
                            print(f"   - 네트워크 오류")
                            return False
                    except Exception as e:
                        print(f"❌ 매수 오류: {symbol} - {str(e)}")
                        print(f"   🔍 오류 상세:")
                        print(f"   - 오류 타입: {type(e).__name__}")
                        print(f"   - 오류 메시지: {str(e)}")
                        return False
                else:
                    print(f"⚠️  매수 스킵: {symbol} (주문금액 부족)")
                    print(f"   📊 계산된 주문금액: {order_amount:,}원")
                    print(f"   📊 최소 주문금액: {self.min_order_amount:,}원")
                    print(f"   💰 사용 가능 KRW: {available_krw:,}원")
                    print(f"   📈 투자 한도 잔여: {remaining_limit:,}원")
                    return False

            elif signal in ['Sell', 'StrongSell']:
                # 매도 로직
                coin_balance = self.upbit.get_balance(coin_name)

                if coin_balance > 0:
                    result = self.upbit.sell_market_order(symbol, coin_balance)
                    if result:
                        # 매수 가격 정보 및 익절 추적 정보 삭제
                        if symbol in self.buy_prices:
                            del self.buy_prices[symbol]
                        if symbol in self.profit_taken_symbols:
                            self.profit_taken_symbols.remove(symbol)
                        print(f"✅ 전량 매도 완료: {symbol} {coin_balance:.8f}개")
                        return True
                    else:
                        print(f"❌ 매도 실패: {symbol}")
                        return False
                else:
                    print(f"⚠️  매도 스킵: {symbol} (보유량 없음)")
                    return False

            return True

        except Exception as e:
            print(f"❌ {symbol} 거래 실행 오류: {e}")
            return False

    def execute_profit_taking(self, symbol: str, sell_amount: float, profit_rate: float) -> bool:
        """익절 실행"""
        try:
            result = self.upbit.sell_market_order(symbol, sell_amount)
            if result:
                print(f"💰 익절 완료: {symbol} {sell_amount:.8f}개 (수익률: {profit_rate:.2f}%)")
                return True
            else:
                print(f"❌ 익절 실패: {symbol}")
                return False
        except Exception as e:
            print(f"❌ {symbol} 익절 실행 오류: {e}")
            return False
    
    def run_analysis_cycle(self):
        """분석 사이클 실행"""
        print(f"\n{'='*60}")
        print(f"🔍 분석 시작: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")

        # 🎯 우선순위 1: 활성된 모든 포지션의 개별 수익률 2% 달성 시 50% 익절 모니터링
        self.monitor_all_positions_for_profit_taking()

        # 계좌 정보 출력
        account = self.get_account_info()
        if 'error' not in account:
            print(f"💰 KRW 잔고: {account['krw_balance']:,.0f}원")
            if account['positions']:
                print("📊 보유 포지션:")
                for symbol, pos in account['positions'].items():
                    print(f"   {symbol}: {pos['balance']:.6f}개 ({pos['value']:,.0f}원)")

                    # 수익률 정보 표시
                    if symbol in self.buy_prices:
                        current_price = pyupbit.get_current_price(symbol)
                        buy_price = self.buy_prices[symbol]
                        profit_rate = (current_price - buy_price) / buy_price * 100
                        print(f"   💹 수익률: {profit_rate:+.2f}% (매수가: {buy_price:,.0f}원)")
            else:
                print("📊 현재 보유 포지션 없음")

        # 각 심볼 분석
        analysis_results = {}
        for symbol in self.symbols:
            print(f"\n📈 {symbol} 분석 중...")

            analysis = self.analyze_symbol(symbol)
            if 'error' in analysis:
                print(f"❌ {symbol} 분석 실패: {analysis['error']}")
                continue

            analysis_results[symbol] = analysis

            # 결과 출력
            composite = analysis['composite_strategy']
            summary = analysis['summary']

            print(f"   현재가: {analysis.get('current_market_price', 0):,.0f}원")
            print(f"   복합신호: {composite['signal']} (신뢰도: {composite['confidence']:.2f})")
            print(f"   개별신호: 매수 {summary['buy_signals']}개, "
                  f"매도 {summary['sell_signals']}개, 보류 {summary['hold_signals']}개")

            # 수익률 정보 출력 (매수 가격이 있는 경우)
            if symbol in self.buy_prices and self.live_trading:
                buy_price = self.buy_prices[symbol]
                current_price = analysis.get('current_market_price', 0)
                profit_rate = (current_price - buy_price) / buy_price * 100
                print(f"   수익률: {profit_rate:.2f}% (매수가: {buy_price:,.0f}원)")

            # 거래 실행
            signal = composite['signal']
            confidence = composite['confidence']

            # 신호 변화 확인 (같은 신호 반복 방지)
            last_signal = self.last_signals.get(symbol)
            if last_signal != signal:
                if signal in ['Buy', 'StrongBuy', 'Sell', 'StrongSell']:
                    success = self.execute_trade(symbol, signal, confidence)
                    if success:
                        self.last_signals[symbol] = signal
                else:
                    print(f"⏸️  {symbol}: 보류 ({signal})")
            else:
                print(f"🔄 {symbol}: 신호 변화 없음 ({signal})")

        return analysis_results
    
    def run_continuous(self, interval_minutes: int = 5):
        """연속 실행"""
        print(f"🔄 연속 실행 시작 (간격: {interval_minutes}분)")
        print("Ctrl+C로 중지할 수 있습니다.")
        
        try:
            while True:
                self.run_analysis_cycle()
                
                print(f"\n⏰ {interval_minutes}분 대기 중...")
                time.sleep(interval_minutes * 60)
                
        except KeyboardInterrupt:
            print("\n🛑 사용자에 의해 중지되었습니다.")
        except Exception as e:
            print(f"\n❌ 시스템 오류: {e}")
    
    def run_single_analysis(self):
        """단일 분석 실행"""
        return self.run_analysis_cycle()


def main():
    """메인 실행 함수"""
    print("🚀 업비트 통합 트레이딩 시스템")
    print("=" * 50)
    
    # 실행 모드 선택
    print("실행 모드를 선택하세요:")
    print("1. 시뮬레이션 모드 (안전)")
    print("2. 실제 거래 모드 (주의!)")
    
    try:
        choice = input("선택 (1 또는 2): ").strip()
        
        if choice == "1":
            live_trading = False
            print("✅ 시뮬레이션 모드 선택")
        elif choice == "2":
            live_trading = True
            confirm = input("⚠️  실제 거래 모드입니다. 정말 진행하시겠습니까? (yes/no): ").strip().lower()
            if confirm != "yes":
                print("❌ 취소되었습니다.")
                return
            print("⚠️  실제 거래 모드 선택")
        else:
            print("❌ 잘못된 선택입니다.")
            return
        
        # 시스템 초기화
        system = IntegratedUpbitSystem(live_trading=live_trading)
        
        # 실행 방식 선택
        print("\n실행 방식을 선택하세요:")
        print("1. 단일 분석")
        print("2. 연속 실행 (5분 간격)")
        
        run_choice = input("선택 (1 또는 2): ").strip()
        
        if run_choice == "1":
            system.run_single_analysis()
        elif run_choice == "2":
            system.run_continuous(5)
        else:
            print("❌ 잘못된 선택입니다.")
            
    except Exception as e:
        print(f"❌ 시스템 오류: {e}")


if __name__ == "__main__":
    main()
