"""
🚀 Bybit 완전 통합 분석 & 자동매매 시스템
- 고급 기술적 분석 (볼린저, CVD, OBV, VPVR)
- RSI6 실시간 자동매매
- 시각화 차트 생성
- 백테스팅 및 리스크 분석
- 다중 실행 모드
"""

import pandas as pd
import numpy as np
import requests
import time
import os
import matplotlib.pyplot as plt
import hmac
import hashlib
import json
import warnings
from datetime import datetime
from scipy.stats import linregress
from config import *

# 경고 무시
warnings.filterwarnings("ignore", category=FutureWarning)

# === 통합 설정 === #
BYBIT_API_KEY = "A1pXxNuW69izQrrinf"
BYBIT_API_SECRET = "KrDbL4GuM3zl4bttuxvNuxy4HSTFN9XIp4GZ"
BYBIT_BASE_URL = "https://api.bybit.com"  # 기본 글로벌 엔드포인트로 변경

# 자동매매 설정 (초고속 스캔)
AUTO_TRADING_ENABLED = True
MAX_TRADE_AMOUNT = 10.0  # 실투자금 $10
LEVERAGE = 3  # 완전 고정: 3배만 사용 (절대 변경 금지)
RSI6_OVERSOLD = 30
SCAN_INTERVAL = 3  # 3초로 단축! (Bybit API 한계 고려)

# 상위 50개 코인 확장 (시가총액 기준)
TOP_50_SYMBOLS = [
    # Top 10
    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'ADAUSDT',
    'DOGEUSDT', 'AVAXUSDT', 'LINKUSDT', 'DOTUSDT', 'LTCUSDT',

    # Top 11-20
    'BCHUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'AAVEUSDT',
    'SUSHIUSDT', 'MATICUSDT', 'ALGOUSDT', 'VETUSDT', 'XLMUSDT',

    # Top 21-30
    'TRXUSDT', 'ICPUSDT', 'NEARUSDT', 'APTUSDT', 'OPUSDT',
    'ARBUSDT', 'INJUSDT', 'MKRUSDT', 'LDOUSDT', 'STXUSDT',

    # Top 31-40
    'IMXUSDT', 'HBARUSDT', 'CROUSDT', 'RNDRUSDT', 'QNTUSDT',
    'GRTUSDT', 'FTMUSDT', 'SANDUSDT', 'MANAUSDT', 'AXSUSDT',

    # Top 41-50
    'THETAUSDT', 'FLOWUSDT', 'CHZUSDT', 'EOSUSDT', 'XTZUSDT',
    'KLAYUSDT', 'ARUSDT', 'GMTUSDT', 'APEUSDT', 'LRCUSDT'
]

class IntegratedTradingSystem:
    def __init__(self):
        self.api_key = BYBIT_API_KEY
        self.api_secret = BYBIT_API_SECRET
        self.executed_trades = {}
        self.analysis_cache = {}
        self.last_calibration_time = 0
        self.rsi6_calibration_offset = {}  # 코인별 RSI6 보정값
        self.tradingview_comparison_log = []

        # main6.py 연동 관련
        self.main6_signals = {}  # main6.py 분석 결과 캐시
        self.last_main6_update = None  # main6.py 마지막 업데이트 시간
        self.main6_analysis_file = './analysis_results/trading_signals.json'

    def load_main6_analysis(self):
        """main6.py 분석 결과 로드"""
        try:
            if not os.path.exists(self.main6_analysis_file):
                return False

            # 파일 수정 시간 확인
            file_mtime = os.path.getmtime(self.main6_analysis_file)
            if self.last_main6_update and file_mtime <= self.last_main6_update:
                return False  # 업데이트 없음

            # 분석 결과 로드
            with open(self.main6_analysis_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 신호 데이터 업데이트
            if 'signals' in data:
                self.main6_signals = data['signals']
                self.last_main6_update = file_mtime
                print(f"📊 main6.py 분석 결과 업데이트: {len(self.main6_signals)}개 신호")
                return True

        except Exception as e:
            print(f"❌ main6.py 데이터 로드 오류: {e}")

        return False

    def get_main6_signal_strength(self, symbol):
        """특정 코인의 main6.py 신호 강도 조회"""
        if symbol in self.main6_signals:
            return self.main6_signals[symbol].get('signal_strength', 0)
        return 0

    def is_main6_recommended(self, symbol):
        """main6.py에서 매수 추천하는 코인인지 확인"""
        if symbol in self.main6_signals:
            signal = self.main6_signals[symbol]
            return (signal.get('recommendation') == 'BUY' and
                   signal.get('signal_strength', 0) >= 60)
        return False
        
    def _generate_signature(self, params, timestamp):
        """API 서명 생성"""
        param_str = timestamp + self.api_key + "5000" + params
        return hmac.new(
            bytes(self.api_secret, "utf-8"),
            param_str.encode("utf-8"),
            hashlib.sha256
        ).hexdigest()
    
    def _make_request(self, endpoint, method="GET", params=None):
        """API 요청"""
        if params is None:
            params = {}
        
        timestamp = str(int(time.time() * 1000))
        
        if method == "GET":
            param_str = "&".join([f"{k}={v}" for k, v in sorted(params.items())])
        else:
            param_str = json.dumps(params) if params else ""
        
        signature = self._generate_signature(param_str, timestamp)
        
        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-RECV-WINDOW": "5000",
            "Content-Type": "application/json"
        }
        
        url = f"{BYBIT_BASE_URL}{endpoint}"
        
        try:
            if method == "GET":
                response = requests.get(url, params=params, headers=headers, timeout=10)
            else:
                response = requests.post(url, json=params, headers=headers, timeout=10)
            
            return response.json()
        except Exception as e:
            print(f"[API] 요청 오류: {e}")
            return None
    
    def fetch_comprehensive_data(self, symbol, interval='15', limit=100):
        """포괄적 데이터 수집 (15분봉)"""
        try:
            url = f"{BYBIT_BASE_URL}/v5/market/kline"
            params = {
                'category': 'linear',
                'symbol': symbol,
                'interval': interval,  # '15' = 15분봉
                'limit': limit
            }

            response = requests.get(url, params=params, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data['retCode'] == 0 and data['result']['list']:
                    klines = data['result']['list']
                    
                    df = pd.DataFrame(klines, columns=[
                        'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
                    ])
                    
                    df['timestamp'] = pd.to_numeric(df['timestamp'])
                    df = df.sort_values('timestamp').reset_index(drop=True)
                    
                    for col in ['open', 'high', 'low', 'close', 'volume']:
                        df[col] = pd.to_numeric(df[col])
                    
                    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                    
                    return df
            
            return None
            
        except Exception as e:
            print(f"[데이터] {symbol} 수집 오류: {e}")
            return None

    def fetch_hourly_data(self, symbol, limit=50):
        """1시간봉 데이터 수집"""
        try:
            url = f"{BYBIT_BASE_URL}/v5/market/kline"
            params = {
                'category': 'linear',
                'symbol': symbol,
                'interval': '60',  # 1시간봉
                'limit': limit
            }

            response = requests.get(url, params=params, timeout=5)
            if response.status_code != 200:
                return None

            data = response.json()
            if data['retCode'] != 0:
                return None

            klines = data['result']['list']
            if not klines:
                return None

            # 데이터 정리 (시간 순서대로)
            klines.reverse()

            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
            ])

            # 데이터 타입 변환
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 타임스탬프는 문자열로 유지 (변환 오류 방지)
            # df['timestamp'] = df['timestamp']  # 그대로 유지

            # RSI6 계산 (1시간봉용)
            df['rsi6_1h'] = self.calculate_rsi6(df['close'].values)

            return df

        except Exception as e:
            print(f"❌ {symbol} 1시간봉 데이터 수집 실패: {e}")
            return None

    def calculate_all_indicators(self, df):
        """모든 기술적 지표 계산"""
        # 기본 가격
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        
        # 이동평균
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma50'] = df['close'].rolling(50).mean()
        df['ma200'] = df['close'].rolling(200).mean()
        
        # 볼린저 밴드
        df['std'] = df['close'].rolling(20).std()
        df['upper_band'] = df['ma20'] + (df['std'] * 2)
        df['lower_band'] = df['ma20'] - (df['std'] * 2)
        
        # RSI (일반)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).fillna(0)
        loss = (-delta.where(delta < 0, 0)).fillna(0)
        avg_gain = gain.rolling(14, min_periods=1).mean()
        avg_loss = loss.rolling(14, min_periods=1).mean()
        rs = avg_gain / avg_loss.replace(0, np.nan).fillna(0.001)
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # RSI6 (매매용) - TradingView 교정 적용
        raw_rsi6 = self.calculate_rsi6(df['close'].values)
        df['rsi6'] = raw_rsi6  # 일단 원본 저장

        # RSI55 (추가 매매 신호용)
        df['rsi55'] = self.calculate_rsi55(df['close'].values)

        # Signal62 (RSI55의 62기간 이동평균)
        df['signal62'] = self.calculate_signal62(df['rsi55'].values)
        
        # CVD (Cumulative Volume Delta)
        df['delta'] = np.where(df['close'] > df['open'], df['volume'], 
                              np.where(df['close'] < df['open'], -df['volume'], 0))
        df['cvd'] = df['delta'].cumsum()
        
        # OBV (On Balance Volume)
        df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
        
        return df
    
    def calculate_rsi6(self, closes):
        """RSI6 계산"""
        if len(closes) < 20:
            return pd.Series([np.nan] * len(closes))
        
        closes = np.array(closes, dtype=float)
        rsi_values = []
        
        for i in range(len(closes)):
            if i < 19:
                rsi_values.append(np.nan)
                continue
                
            period_closes = closes[max(0, i-50):i+1]
            if len(period_closes) < 20:
                rsi_values.append(np.nan)
                continue
                
            deltas = np.diff(period_closes)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            period = 6
            avg_gain = np.mean(gains[:period])
            avg_loss = np.mean(losses[:period])
            
            for j in range(period, len(gains)):
                avg_gain = ((avg_gain * (period - 1)) + gains[j]) / period
                avg_loss = ((avg_loss * (period - 1)) + losses[j]) / period
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            rsi_values.append(rsi)
        
        return pd.Series(rsi_values)

    def calculate_rsi55(self, closes):
        """RSI55 계산 (55기간)"""
        if len(closes) < 60:
            return pd.Series([50] * len(closes))

        closes = np.array(closes, dtype=float)
        rsi_values = []

        for i in range(len(closes)):
            if i < 55:
                rsi_values.append(50)
                continue

            period_closes = closes[max(0, i-55):i+1]
            deltas = np.diff(period_closes)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            period = 55
            avg_gain = np.mean(gains[:period])
            avg_loss = np.mean(losses[:period])

            for j in range(period, len(gains)):
                avg_gain = ((avg_gain * (period - 1)) + gains[j]) / period
                avg_loss = ((avg_loss * (period - 1)) + losses[j]) / period

            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))

            rsi_values.append(rsi)

        return pd.Series(rsi_values)

    def calculate_signal62(self, rsi55_values):
        """Signal62 계산 (RSI55의 62기간 이동평균)"""
        if len(rsi55_values) < 62:
            return pd.Series([50] * len(rsi55_values))

        signal_values = []
        for i in range(len(rsi55_values)):
            if i < 61:  # 62기간이므로 61개 이전은 기본값
                signal_values.append(50)
            else:
                # 62기간 이동평균
                period_rsi = rsi55_values[i-61:i+1]
                signal = np.mean(period_rsi)
                signal_values.append(signal)

        return pd.Series(signal_values)

    def calibrate_rsi6_with_tradingview(self, symbol, calculated_rsi6):
        """TradingView와 RSI6 비교 및 교정 (단순화)"""
        # 교정 기능 비활성화 - 원본 RSI6 값 그대로 사용
        return calculated_rsi6

    def perform_tradingview_calibration(self):
        """TradingView 기준 RSI6 교정 수행"""
        print(f"\n🔧 TradingView RSI6 교정 시작 - {datetime.now().strftime('%H:%M:%S')}")
        print("=" * 80)

        # 주요 코인들에 대한 TradingView 기준값 (수동 입력 또는 API)
        # 실제로는 TradingView API나 웹스크래핑으로 가져와야 하지만,
        # 여기서는 사용자 입력 방식으로 구현

        major_coins = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'DOGEUSDT']

        print("📊 주요 코인 RSI6 교정 (TradingView 15분봉 기준)")
        print("💡 TradingView에서 확인한 RSI6 값을 입력하세요 (Enter로 스킵)")

        for symbol in major_coins:
            name = symbol.replace('USDT', '')

            # 현재 계산된 RSI6 값
            analysis = self.comprehensive_analysis(symbol)
            if analysis:
                calculated_rsi6 = analysis['current_rsi6']

                print(f"\n🔍 {name}:")
                print(f"   계산된 RSI6: {calculated_rsi6:.1f}")

                try:
                    # 사용자 입력 (실제 환경에서는 자동화 가능)
                    tradingview_input = input(f"   TradingView RSI6 (현재: {calculated_rsi6:.1f}): ").strip()

                    if tradingview_input and tradingview_input.replace('.', '').isdigit():
                        tradingview_rsi6 = float(tradingview_input)
                        offset = tradingview_rsi6 - calculated_rsi6

                        self.rsi6_calibration_offset[symbol] = offset

                        print(f"   ✅ 보정값: {offset:+.1f} (TradingView: {tradingview_rsi6:.1f})")

                        # 로그 저장
                        self.tradingview_comparison_log.append({
                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'symbol': symbol,
                            'calculated': calculated_rsi6,
                            'tradingview': tradingview_rsi6,
                            'offset': offset
                        })
                    else:
                        print(f"   ⏭️ 스킵됨")

                except KeyboardInterrupt:
                    print(f"\n⏹️ 교정 중단됨")
                    break
                except:
                    print(f"   ❌ 입력 오류, 스킵됨")

        # 교정 결과 저장
        if self.tradingview_comparison_log:
            with open('rsi6_calibration_log.txt', 'a', encoding='utf-8') as f:
                for log in self.tradingview_comparison_log[-len(major_coins):]:
                    f.write(f"{log['time']} - {log['symbol']}: "
                           f"계산값 {log['calculated']:.1f} → TradingView {log['tradingview']:.1f} "
                           f"(보정: {log['offset']:+.1f})\n")

        print("=" * 80)
        print("🔧 RSI6 교정 완료! 다음 교정은 1시간 후입니다.")
        print("=" * 80)
    
    def comprehensive_analysis(self, symbol):
        """포괄적 분석 (15분봉 + 1시간봉 + main6.py 연동)"""
        # main6.py 분석 결과 업데이트 확인
        self.load_main6_analysis()

        # 15분봉 데이터 수집
        df_15m = self.fetch_comprehensive_data(symbol)
        if df_15m is None or len(df_15m) < 50:
            return None

        # 1시간봉 데이터 수집
        df_1h = self.fetch_hourly_data(symbol)
        if df_1h is None or len(df_1h) < 10:
            return None
        
        # 15분봉 지표 계산
        df_15m = self.calculate_all_indicators(df_15m)

        # 15분봉 캔들 정보
        current_candle_15m = df_15m.iloc[-1]
        prev_candle_15m = df_15m.iloc[-2]

        # 1시간봉 캔들 정보
        current_candle_1h = df_1h.iloc[-1]
        prev_candle_1h = df_1h.iloc[-2] if len(df_1h) > 1 else current_candle_1h

        # 15분봉 RSI6 (기존 로직)
        raw_current_rsi6_15m = current_candle_15m['rsi6']
        raw_prev_rsi6_15m = prev_candle_15m['rsi6']
        current_rsi6_15m = self.calibrate_rsi6_with_tradingview(symbol, raw_current_rsi6_15m)
        prev_rsi6_15m = self.calibrate_rsi6_with_tradingview(symbol, raw_prev_rsi6_15m)

        # 1시간봉 RSI6 (새로운 로직)
        current_rsi6_1h = current_candle_1h['rsi6_1h']
        prev_rsi6_1h = prev_candle_1h['rsi6_1h']
        
        # 매수 신호들
        # 1. 15분봉 RSI6 30 상향 돌파 (기존)
        rsi6_15m_breakout = (prev_rsi6_15m < RSI6_OVERSOLD) and (current_rsi6_15m >= RSI6_OVERSOLD)

        # 2. 1시간봉 RSI6 30 상향 돌파 (새로운)
        rsi6_1h_breakout = (prev_rsi6_1h < RSI6_OVERSOLD) and (current_rsi6_1h >= RSI6_OVERSOLD)

        # 3. 1시간봉 RSI6 30 근처에서 양봉 + 거래량 (새로운)
        rsi6_1h_near_30 = (25 <= current_rsi6_1h <= 35)  # 30 근처
        bullish_candle_1h = current_candle_1h['close'] > current_candle_1h['open']  # 양봉
        volume_surge_1h = current_candle_1h['volume'] > df_1h['volume'].tail(10).mean() * 1.2  # 거래량 증가
        rsi6_1h_near_signal = rsi6_1h_near_30 and bullish_candle_1h and volume_surge_1h

        # RSI55 및 Signal62 값 추출 (15분봉 기준)
        current_rsi55 = current_candle_15m['rsi55']
        prev_rsi55 = prev_candle_15m['rsi55']
        current_signal62 = current_candle_15m['signal62']
        prev_signal62 = prev_candle_15m['signal62']

        # 추가 매수 신호들 (15분봉 기준)
        rsi55_50_breakout = (prev_rsi55 < 50) and (current_rsi55 >= 50)  # RSI55 50 상향 돌파
        rsi55_signal_breakout = (prev_rsi55 < prev_signal62) and (current_rsi55 >= current_signal62)  # RSI55가 Signal62 상향 돌파
        
        # 과매도 패턴 (15분봉 기준)
        recent_rsi6 = df_15m['rsi6'].tail(5).values
        oversold_pattern = sum(1 for rsi in recent_rsi6 if rsi <= RSI6_OVERSOLD) >= 3

        # 볼린저 밴드 신호 (15분봉 기준)
        bb_support = current_candle_15m['close'] <= current_candle_15m['lower_band']
        bb_squeeze = (current_candle_15m['upper_band'] - current_candle_15m['lower_band']) / current_candle_15m['ma20'] < 0.1

        # 거래량 분석 (15분봉 기준)
        avg_volume = df_15m['volume'].tail(20).mean()
        volume_surge = current_candle_15m['volume'] > avg_volume * 1.5

        # CVD 분석 (15분봉 기준)
        cvd_trend = current_candle_15m['cvd'] > prev_candle_15m['cvd']
        
        # 종합 매수 신호
        rsi6_15m_signal = rsi6_15m_breakout and oversold_pattern  # 15분봉 RSI6 30 돌파
        rsi6_1h_signal = rsi6_1h_breakout  # 1시간봉 RSI6 30 돌파
        rsi6_1h_near_buy = rsi6_1h_near_signal  # 1시간봉 RSI6 30 근처 + 양봉 + 거래량
        rsi55_50_signal = rsi55_50_breakout and volume_surge  # RSI55 50 돌파 신호
        rsi55_signal_signal = rsi55_signal_breakout and volume_surge  # RSI55가 Signal62 돌파 신호

        # main6.py 분석 결과 반영
        main6_signal_strength = self.get_main6_signal_strength(symbol)
        main6_recommended = self.is_main6_recommended(symbol)
        main6_boost = main6_signal_strength >= 70  # 강한 신호일 때 부스트

        # 통합 기본 신호 (6가지 조건 중 하나라도 충족 + main6.py 연동)
        basic_signal = (rsi6_15m_signal or rsi6_1h_signal or rsi6_1h_near_buy or
                       rsi55_50_signal or rsi55_signal_signal or main6_recommended)

        # 강화된 신호 (main6.py 부스트 포함)
        enhanced_signal = basic_signal and (bb_support or volume_surge or cvd_trend or main6_boost)
        
        # 포지션 계산 (3배 레버리지 완전 고정) - 15분봉 기준
        entry_price = float(current_candle_15m['close'])
        stop_loss_price = float(prev_candle_15m['low'])

        # $10 투자금 × 3배 레버리지 = $30 노출 (하드코딩)
        investment_amount = 10.0  # 실투자금 $10 고정
        leverage_multiplier = 3   # 3배 레버리지 고정
        total_exposure = investment_amount * leverage_multiplier  # $30 고정
        qty = total_exposure / entry_price  # 실제 매수 수량

        return {
            'symbol': symbol,
            'basic_signal': basic_signal,
            'enhanced_signal': enhanced_signal,
            'entry_price': entry_price,
            'stop_loss_price': stop_loss_price,
            'qty': qty,
            'current_rsi6': current_rsi6_15m,  # 15분봉 RSI6
            'prev_rsi6': prev_rsi6_15m,  # 15분봉 RSI6
            'current_rsi6_1h': current_rsi6_1h,  # 1시간봉 RSI6 추가
            'prev_rsi6_1h': prev_rsi6_1h,  # 1시간봉 RSI6 추가
            # main6.py 연동 정보 추가
            'main6_signal_strength': main6_signal_strength,
            'main6_recommended': main6_recommended,
            'main6_boost': main6_boost,
            'bb_support': bb_support,
            'volume_surge': volume_surge,
            'cvd_trend': cvd_trend,
            'dataframe': df_15m,  # 15분봉 데이터
            'dataframe_1h': df_1h  # 1시간봉 데이터 추가
        }
    
    def create_visualization(self, analysis):
        """시각화 생성"""
        if not analysis or analysis['dataframe'] is None:
            return None
        
        symbol = analysis['symbol']
        df = analysis['dataframe'].tail(100)  # 최근 100개
        
        plt.figure(figsize=(16, 12))
        
        # 1. 가격 + 볼린저 밴드
        plt.subplot(3, 1, 1)
        plt.plot(df['datetime'], df['close'], label='Price', color='black', linewidth=2)
        plt.plot(df['datetime'], df['ma20'], label='MA20', color='blue', alpha=0.7)
        plt.fill_between(df['datetime'], df['upper_band'], df['lower_band'], 
                         color='gray', alpha=0.2, label='Bollinger Bands')
        
        # 매수 신호 표시
        if analysis['enhanced_signal']:
            plt.scatter(df['datetime'].iloc[-1], analysis['entry_price'], 
                       color='red', s=100, marker='^', label='BUY SIGNAL')
        
        plt.title(f'{symbol} - Price & Bollinger Bands')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. RSI6 + RSI14
        plt.subplot(3, 1, 2)
        plt.plot(df['datetime'], df['rsi6'], label='RSI6', color='red', linewidth=2)
        plt.plot(df['datetime'], df['rsi'], label='RSI14', color='purple', alpha=0.7)
        plt.axhline(y=70, color='r', linestyle='--', alpha=0.5)
        plt.axhline(y=30, color='g', linestyle='--', alpha=0.5)
        plt.fill_between(df['datetime'], 70, 100, color='red', alpha=0.1)
        plt.fill_between(df['datetime'], 0, 30, color='green', alpha=0.1)
        
        plt.title('RSI6 & RSI14')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 3. CVD + Volume
        plt.subplot(3, 1, 3)
        plt.plot(df['datetime'], df['cvd'], label='CVD', color='green', linewidth=2)
        plt.bar(df['datetime'], df['volume'], alpha=0.3, color='blue', label='Volume')
        
        plt.title('CVD & Volume')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 저장
        filename = f"{symbol}_integrated_analysis.png"
        save_path = os.path.join(SAVE_DIR if 'SAVE_DIR' in globals() else '.', filename)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        return save_path

    def set_leverage(self, symbol, leverage=3):
        """레버리지 설정 (3배 고정)"""
        endpoint = "/v5/position/set-leverage"
        params = {
            "category": "linear",
            "symbol": symbol,
            "buyLeverage": str(leverage),  # 3배 고정
            "sellLeverage": str(leverage)  # 3배 고정
        }

        result = self._make_request(endpoint, "POST", params)
        return result

    def place_order(self, symbol, side, qty, order_type="Market", stop_loss=None):
        """주문 실행 (레버리지 3배 설정 포함)"""
        # 먼저 레버리지를 3배로 설정
        leverage_result = self.set_leverage(symbol, 3)
        if leverage_result and leverage_result.get('retCode') == 0:
            print(f"   🔧 레버리지 3배 설정 완료: {symbol}")
        else:
            print(f"   ⚠️ 레버리지 설정 실패: {symbol}")

        endpoint = "/v5/order/create"

        # 최소 수량 처리 (50개 코인)
        min_qty_rules = {
            # Top 10
            'BTCUSDT': 0.001, 'ETHUSDT': 0.01, 'SOLUSDT': 0.1, 'XRPUSDT': 1,
            'ADAUSDT': 1, 'DOGEUSDT': 1, 'AVAXUSDT': 0.1, 'LINKUSDT': 0.1,
            'DOTUSDT': 0.1, 'LTCUSDT': 0.01,

            # Top 11-20
            'BCHUSDT': 0.001, 'UNIUSDT': 0.1, 'ATOMUSDT': 0.1, 'FILUSDT': 0.1,
            'AAVEUSDT': 0.01, 'SUSHIUSDT': 0.1, 'MATICUSDT': 1, 'ALGOUSDT': 1,
            'VETUSDT': 1, 'XLMUSDT': 1,

            # Top 21-30
            'TRXUSDT': 1, 'ICPUSDT': 0.1, 'NEARUSDT': 0.1, 'APTUSDT': 0.1,
            'OPUSDT': 0.1, 'ARBUSDT': 0.1, 'INJUSDT': 0.1, 'MKRUSDT': 0.001,
            'LDOUSDT': 0.1, 'STXUSDT': 0.1,

            # Top 31-40
            'IMXUSDT': 0.1, 'HBARUSDT': 1, 'CROUSDT': 1, 'RNDRUSDT': 0.1,
            'QNTUSDT': 0.01, 'GRTUSDT': 1, 'FTMUSDT': 0.1, 'SANDUSDT': 1,
            'MANAUSDT': 1, 'AXSUSDT': 0.1,

            # Top 41-50
            'THETAUSDT': 0.1, 'FLOWUSDT': 0.1, 'CHZUSDT': 1, 'EOSUSDT': 0.1,
            'XTZUSDT': 0.1, 'KLAYUSDT': 1, 'ARUSDT': 0.1, 'GMTUSDT': 1,
            'APEUSDT': 0.1, 'LRCUSDT': 1
        }

        min_qty = min_qty_rules.get(symbol, 0.1)
        qty = max(float(qty), min_qty)

        # 수량 반올림 (50개 코인)
        # 정수 단위 코인들
        integer_coins = ['DOGEUSDT', 'XRPUSDT', 'ADAUSDT', 'MATICUSDT', 'ALGOUSDT',
                        'VETUSDT', 'XLMUSDT', 'TRXUSDT', 'HBARUSDT', 'CROUSDT',
                        'GRTUSDT', 'SANDUSDT', 'MANAUSDT', 'CHZUSDT', 'KLAYUSDT',
                        'GMTUSDT', 'LRCUSDT']

        # 소수점 4자리 코인들 (고가)
        high_precision_coins = ['BTCUSDT', 'BCHUSDT', 'MKRUSDT']

        # 소수점 2자리 코인들 (중간가)
        medium_precision_coins = ['ETHUSDT', 'LTCUSDT', 'AAVEUSDT', 'QNTUSDT']

        if symbol in integer_coins:
            qty = max(round(qty, 0), 1)
        elif symbol in high_precision_coins:
            qty = round(qty, 4)
        elif symbol in medium_precision_coins:
            qty = round(qty, 2)
        else:
            qty = round(qty, 1)  # 기본값: 소수점 1자리

        params = {
            "category": "linear",
            "symbol": symbol,
            "side": side,
            "orderType": order_type,
            "qty": str(qty),
            "timeInForce": "GTC"
        }

        if stop_loss:
            params["stopLoss"] = str(round(float(stop_loss), 6))

        return self._make_request(endpoint, "POST", params)

    def get_positions(self):
        """포지션 조회"""
        endpoint = "/v5/position/list"
        params = {"category": "linear"}

        result = self._make_request(endpoint, "GET", params)

        if result and result.get('retCode') == 0:
            return result['result']['list']
        return []

    def get_current_price(self, symbol):
        """현재가 조회"""
        try:
            endpoint = "/v5/market/tickers"
            params = {"category": "linear", "symbol": symbol}
            result = self._make_request(endpoint, "GET", params)

            if result and result.get('retCode') == 0:
                return float(result['result']['list'][0]['lastPrice'])
        except:
            pass
        return None

    def calculate_take_profit_levels(self, entry_price, position_size):
        """익절 목표가 계산 (2% 50% 익절 포함)"""
        entry_price = float(entry_price)
        position_size = float(position_size)

        # 4단계 분할 익절 전략 (3배 레버리지 고정, 실제 수익률 기준)
        levels = [
            {
                'level': 0,  # 실제 수익률 2% 익절 레벨
                'price': entry_price * 1.0067,  # +0.67% 가격상승 (3배 레버리지 = +2% 실제수익)
                'quantity': position_size * 0.50,  # 50% 익절
                'description': '실제수익 2% 익절 (50%)'
            },
            {
                'level': 1,
                'price': entry_price * 1.021,  # +2.1% 가격상승 (3배 레버리지 = +6.3% 실제수익)
                'quantity': position_size * 0.15,  # 15% 익절
                'description': '1차 익절 (15%) 실제수익 +6.3%'
            },
            {
                'level': 2,
                'price': entry_price * 1.045,  # +4.5% 가격상승 (3배 레버리지 = +13.5% 실제수익)
                'quantity': position_size * 0.20,  # 20% 익절
                'description': '2차 익절 (20%) 실제수익 +13.5%'
            },
            {
                'level': 3,
                'price': entry_price * 1.075,  # +7.5% 가격상승 (3배 레버리지 = +22.5% 실제수익)
                'quantity': position_size * 0.15,  # 15% 익절
                'description': '3차 익절 (15%) 실제수익 +22.5%'
            }
        ]

        return levels

    def check_profit_taking_2percent(self, symbol, entry_price, current_price, position_size):
        """실제 수익률 2% 달성 시 50% 익절 체크"""
        try:
            entry_price = float(entry_price)
            current_price = float(current_price)
            position_size = float(position_size)

            # 수익률 계산 (3배 레버리지 고려)
            price_change_pct = ((current_price - entry_price) / entry_price) * 100
            profit_pct = price_change_pct * 3  # 3배 레버리지로 실제 수익률

            # 실제 수익률 2% 달성 시 (가격 0.67% 상승)
            if profit_pct >= 2.0:
                sell_quantity = position_size * 0.50  # 50% 익절
                return {
                    'should_sell': True,
                    'quantity': sell_quantity,
                    'profit_pct': profit_pct,
                    'price_change_pct': price_change_pct,
                    'reason': f'실제수익 2% 익절 (50% 물량, 가격상승 {price_change_pct:.2f}%)'
                }

            return {'should_sell': False, 'profit_pct': profit_pct}

        except Exception as e:
            print(f"❌ {symbol} 익절 체크 오류: {e}")
            return {'should_sell': False, 'profit_pct': 0}

    def check_technical_sell_signals(self, symbol, entry_price, current_price):
        """기술적 매도 신호 확인 (RSI6 65 기준)"""
        analysis = self.comprehensive_analysis(symbol)

        if not analysis:
            return False, []

        sell_signals = []
        df = analysis['dataframe']

        # 🔥 핵심 매도 신호들
        current_rsi6 = analysis['current_rsi6']
        prev_rsi6 = analysis['prev_rsi6']

        # RSI55 및 Signal62 값 추출
        df = analysis['dataframe']
        current_rsi55 = df['rsi55'].iloc[-1]
        prev_rsi55 = df['rsi55'].iloc[-2] if len(df) > 1 else current_rsi55
        current_signal62 = df['signal62'].iloc[-1]
        prev_signal62 = df['signal62'].iloc[-2] if len(df) > 1 else current_signal62

        # 매도 신호들
        rsi6_sell_signal = (prev_rsi6 >= 65) and (current_rsi6 < 65)  # RSI6 65 하락돌파
        rsi55_49_sell_signal = (prev_rsi55 > 49) and (current_rsi55 <= 49)  # RSI55 49 하향돌파
        rsi55_signal_sell_signal = (prev_rsi55 > prev_signal62) and (current_rsi55 <= current_signal62)  # RSI55가 Signal62 하향돌파

        # 우선순위 매도 신호들 (즉시 매도)
        if rsi6_sell_signal:
            sell_signals.append("RSI6 65 하락돌파")
            return True, sell_signals

        if rsi55_49_sell_signal:
            sell_signals.append("RSI55 49 하향돌파")
            return True, sell_signals

        if rsi55_signal_sell_signal:
            sell_signals.append("RSI55→Signal62 하향돌파")
            return True, sell_signals

        # 추가 기술적 매도 신호들 (보조 신호)
        # RSI6 과매수 극한 (75 이상)
        if current_rsi6 >= 75:
            sell_signals.append("RSI6 극과매수")

        # 볼린저 밴드 상단 터치/돌파
        if len(df) > 0:
            current_candle = df.iloc[-1]
            if current_price >= current_candle['upper_band']:
                sell_signals.append("볼린저 상단 돌파")

        # CVD 하락 전환 (최근 3개 하락)
        if len(df) >= 4:
            recent_cvd = df['cvd'].tail(4).values
            if all(recent_cvd[i] > recent_cvd[i+1] for i in range(2)):
                sell_signals.append("CVD 하락 전환")

        # 수익률 기반 (높은 수익 시 기술적 매도 고려)
        price_change_pct = ((current_price - entry_price) / entry_price) * 100
        profit_pct = price_change_pct * 3  # 3배 레버리지 완전 고정
        if profit_pct >= 25:  # 3배 레버리지 기준 25% 이상 수익
            sell_signals.append("고수익 구간")

        # RSI6 65 하락돌파가 아닌 경우, 2개 이상 신호 시 매도
        return len(sell_signals) >= 2, sell_signals

    def execute_sell_order(self, symbol, quantity, reason, current_price):
        """매도 주문 실행"""
        print(f"   🎯 {symbol.replace('USDT', '')} 매도 실행: {reason}")
        print(f"   💰 매도가: ${current_price:.6f}")
        print(f"   📦 수량: {quantity:.6f}")

        order_result = self.place_order(
            symbol=symbol,
            side="Sell",
            qty=quantity,
            order_type="Market"
        )

        if order_result and order_result.get('retCode') == 0:
            print(f"   ✅ 매도 성공! ID: {order_result['result'].get('orderId', 'N/A')}")

            # 매도 로그 저장
            with open('sell_trading_log.txt', 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"{timestamp} - {symbol} 매도: "
                       f"${current_price:.6f}, 수량: {quantity:.6f}, "
                       f"사유: {reason}\n")

            return True
        else:
            print(f"   ❌ 매도 실패")
            if order_result:
                print(f"   오류: {order_result.get('retMsg', 'Unknown')}")
            return False

    def monitor_sell_signals(self):
        """매도 신호 모니터링 및 실행"""
        positions = self.get_positions()

        if not positions:
            return

        print(f"\n💰 포지션 모니터링 ({len(positions)}개)")

        for position in positions:
            if position['side'] != 'Buy' or float(position['size']) == 0:
                continue

            symbol = position['symbol']
            entry_price = float(position['entryPrice'])
            position_size = float(position['size'])
            unrealized_pnl = float(position['unrealisedPnl'])

            # 현재가 조회
            current_price = self.get_current_price(symbol)
            if not current_price:
                continue

            name = symbol.replace('USDT', '')

            # 수익률 계산 (3배 레버리지 완전 고정)
            price_change_pct = ((current_price - entry_price) / entry_price) * 100
            profit_pct = price_change_pct * 3  # 3배 레버리지 하드코딩

            print(f"   📊 {name}: ${current_price:.6f} (진입: ${entry_price:.6f}) "
                  f"수익: {profit_pct:+.1f}% (${unrealized_pnl:+.2f})")

            # 포지션별 익절 추적 (메모리에 저장)
            if symbol not in self.executed_trades:
                self.executed_trades[symbol] = {
                    'entry_price': entry_price,
                    'original_size': position_size,
                    'executed_levels': [],
                    'rsi6_history': [],
                    'max_rsi6': 0
                }

            position_info = self.executed_trades[symbol]
            executed_levels = position_info.get('executed_levels', [])

            # RSI6 히스토리 업데이트
            current_analysis = self.comprehensive_analysis(symbol)
            if current_analysis:
                current_rsi6 = current_analysis['current_rsi6']
                rsi6_history = position_info.get('rsi6_history', [])
                max_rsi6 = position_info.get('max_rsi6', 0)

                # RSI6 히스토리에 추가 (최근 10개만 유지)
                rsi6_history.append(current_rsi6)
                if len(rsi6_history) > 10:
                    rsi6_history = rsi6_history[-10:]

                # 최고 RSI6 업데이트
                max_rsi6 = max(max_rsi6, current_rsi6)

                position_info['rsi6_history'] = rsi6_history
                position_info['max_rsi6'] = max_rsi6

                print(f"   📊 {name}: RSI6 {current_rsi6:.1f} (최고: {max_rsi6:.1f})")

                # 🔥 RSI6 65 하락 돌파 체크 (우선순위 1)
                if len(rsi6_history) >= 2:
                    prev_rsi6 = rsi6_history[-2]
                    rsi6_decline_signal = (prev_rsi6 >= 65) and (current_rsi6 < 65)

                    if rsi6_decline_signal and position_size > 0:
                        print(f"   🚨 {name} RSI6 65 하락돌파! {prev_rsi6:.1f} → {current_rsi6:.1f}")
                        if self.execute_sell_order(symbol, position_size, "RSI6 65 하락돌파", current_price):
                            print(f"   🏁 {name} RSI6 매도 완료!")
                            if symbol in self.executed_trades:
                                del self.executed_trades[symbol]
                            continue

            # 🎯 실제 수익률 2% 익절 체크 (50% 물량) - RSI6 하락돌파 다음 우선순위
            profit_check = self.check_profit_taking_2percent(symbol, entry_price, current_price, position_size)
            if profit_check['should_sell'] and position_size > 0:
                # 이미 실제 수익률 2% 익절을 실행했는지 확인
                if 0 not in executed_levels:  # level 0이 실제 수익률 2% 익절
                    print(f"   💰 {name} {profit_check['reason']}")
                    if self.execute_sell_order(symbol, profit_check['quantity'], profit_check['reason'], current_price):
                        executed_levels.append(0)  # 실제 수익률 2% 익절 완료 표시
                        position_info['executed_levels'] = executed_levels
                        print(f"   ✅ {name} 실제 수익률 2% 익절 완료! 남은 50% 물량으로 추가 수익 추구")
                        continue

            # 1. 분할 익절 확인
            take_profit_levels = self.calculate_take_profit_levels(entry_price, position_info['original_size'])

            for level_info in take_profit_levels:
                level = level_info['level']
                target_price = level_info['price']
                target_quantity = level_info['quantity']
                description = level_info['description']

                # 아직 실행되지 않은 레벨이고 목표가에 도달했으면
                if level not in executed_levels and current_price >= target_price:
                    # 현재 포지션 크기에 맞게 수량 조정
                    actual_quantity = min(target_quantity, position_size)

                    if self.execute_sell_order(symbol, actual_quantity, description, current_price):
                        executed_levels.append(level)
                        position_info['executed_levels'] = executed_levels
                        print(f"   🎉 {name} {description} 완료!")
                        break

            # 2. 기술적 매도 신호 확인
            should_sell, sell_signals = self.check_technical_sell_signals(symbol, entry_price, current_price)

            if should_sell and position_size > 0:
                print(f"   🚨 {name} 기술적 매도 신호: {', '.join(sell_signals)}")

                # 남은 포지션 전량 매도
                if self.execute_sell_order(symbol, position_size, f"기술적 매도: {', '.join(sell_signals)}", current_price):
                    print(f"   🏁 {name} 전량 매도 완료!")
                    # 포지션 정보 제거
                    if symbol in self.executed_trades:
                        del self.executed_trades[symbol]

            # 3. 리스크 관리 손절 (2단계)
            position_info = self.executed_trades.get(symbol, {})

            # 1차 손절: 전봉 저가 하락
            if 'stop_loss_price' in position_info:
                stop_loss_price = position_info['stop_loss_price']
                if current_price < stop_loss_price:
                    print(f"   🛑 {name} 전봉 저가 손절! ${current_price:.6f} < ${stop_loss_price:.6f}")
                    if self.execute_sell_order(symbol, position_size, "전봉 저가 손절", current_price):
                        if symbol in self.executed_trades:
                            del self.executed_trades[symbol]
                        continue

            # 2차 손절: 실제 -5% 손실
            actual_loss_pct = ((current_price - entry_price) / entry_price) * 100
            if actual_loss_pct <= -5:  # 실제 -5% 손실 (레버리지 무관)
                print(f"   🛑 {name} -5% 손절! 실제손실: {actual_loss_pct:.1f}%")
                if self.execute_sell_order(symbol, position_size, "-5% 손절", current_price):
                    if symbol in self.executed_trades:
                        del self.executed_trades[symbol]

    def execute_integrated_trading(self, symbols):
        """통합 자동매매 실행 (50코인 초고속)"""
        scan_num = len([k for k in self.executed_trades.keys() if isinstance(self.executed_trades[k], dict)])+1
        print(f"\n🚀 초고속 50코인 스캔 #{scan_num} - {datetime.now().strftime('%H:%M:%S')}")
        print("-" * 90)

        # 1. 먼저 매도 신호 모니터링 (기존 포지션)
        self.monitor_sell_signals()

        # 2. 새로운 매수 신호 스캔
        signals_found = 0
        enhanced_signals = 0

        for symbol in symbols:
            # 이미 포지션이 있는 경우 새로운 매수 스킵
            if symbol in self.executed_trades and isinstance(self.executed_trades[symbol], dict):
                continue

            try:
                analysis = self.comprehensive_analysis(symbol)

                if analysis:
                    name = symbol.replace('USDT', '')

                    # 기본 신호
                    if analysis['basic_signal']:
                        signals_found += 1
                        print(f"📊 {name}: 기본 RSI6 신호")
                        print(f"   RSI6: {analysis['prev_rsi6']:.1f} → {analysis['current_rsi6']:.1f}")

                    # 강화 신호
                    if analysis['enhanced_signal']:
                        enhanced_signals += 1
                        print(f"🚨 {name}: 강화된 매수 신호!")
                        print(f"   💰 진입가: ${analysis['entry_price']:.6f}")
                        print(f"   🛑 손절가: ${analysis['stop_loss_price']:.6f}")
                        print(f"   📊 RSI6: {analysis['prev_rsi6']:.1f} → {analysis['current_rsi6']:.1f}")

                        # 신호 상세
                        signals = []
                        if analysis['bb_support']:
                            signals.append("볼린저하단")
                        if analysis['volume_surge']:
                            signals.append("거래량급증")
                        if analysis['cvd_trend']:
                            signals.append("CVD상승")

                        print(f"   🎯 추가신호: {', '.join(signals)}")

                        # 시각화 생성
                        chart_path = self.create_visualization(analysis)
                        if chart_path:
                            print(f"   📈 차트저장: {chart_path}")

                        # 자동매매 실행
                        if AUTO_TRADING_ENABLED:
                            print(f"   🚀 자동 매수 실행 중...")

                            order_result = self.place_order(
                                symbol=symbol,
                                side="Buy",
                                qty=analysis['qty'],
                                order_type="Market",
                                stop_loss=analysis['stop_loss_price']
                            )

                            if order_result and order_result.get('retCode') == 0:
                                print(f"   ✅ 매수 성공! ID: {order_result['result'].get('orderId', 'N/A')}")

                                # 포지션 정보 저장 (매도 추적용)
                                self.executed_trades[symbol] = {
                                    'entry_price': analysis['entry_price'],
                                    'original_size': analysis['qty'],
                                    'executed_levels': [],
                                    'entry_time': datetime.now(),
                                    'stop_loss_price': analysis['stop_loss_price'],  # 전봉 저가 저장
                                    'rsi6_history': [analysis['current_rsi6']],  # RSI6 히스토리 추적
                                    'max_rsi6': analysis['current_rsi6']  # 최고 RSI6 기록
                                }

                                # 로그 저장
                                with open('integrated_trading_log.txt', 'a', encoding='utf-8') as f:
                                    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                    f.write(f"{timestamp} - {symbol} 통합매수: "
                                           f"${analysis['entry_price']:.6f}, "
                                           f"신호: {', '.join(signals)}, "
                                           f"RSI6: {analysis['prev_rsi6']:.1f}→{analysis['current_rsi6']:.1f}\n")
                            else:
                                print(f"   ❌ 매수 실패")
                                if order_result:
                                    print(f"   오류: {order_result.get('retMsg', 'Unknown')}")

                    elif analysis['current_rsi6'] <= RSI6_OVERSOLD:
                        print(f"👀 {name}: RSI6 {analysis['current_rsi6']:.1f} (과매도 대기)")

                    # 모든 코인의 RSI6 값을 간단히 수집 (나중에 한 번에 출력)
                    if not hasattr(self, 'current_scan_rsi'):
                        self.current_scan_rsi = []
                    self.current_scan_rsi.append(f"{name}:{analysis['current_rsi6']:.0f}")

            except Exception as e:
                print(f"[오류] {symbol}: {e}")
                continue

        print("-" * 90)
        active_positions = len([k for k in self.executed_trades.keys() if isinstance(self.executed_trades[k], dict)])
        print(f"🚀 기본신호: {signals_found}개 | 강화신호: {enhanced_signals}개 | 활성포지션: {active_positions}개 | 스캔: {len(symbols)}코인")

        # 모든 코인의 RSI6 값 표시 (10개씩)
        if hasattr(self, 'current_scan_rsi') and self.current_scan_rsi:
            print("📊 RSI6 현황:", end=" ")
            for i, rsi_info in enumerate(self.current_scan_rsi):
                print(rsi_info, end="  ")
                if (i + 1) % 10 == 0:
                    print()
                    if i + 1 < len(self.current_scan_rsi):
                        print("           ", end=" ")
            if len(self.current_scan_rsi) % 10 != 0:
                print()
            self.current_scan_rsi = []  # 초기화

    def run_integrated_system(self, symbols, mode='auto'):
        """통합 시스템 실행 (초고속 50코인)"""
        print("🚀 Bybit 초고속 통합 분석 & 자동매매 시스템 (TOP 50)")
        print("=" * 100)
        print(f"💰 실투자금: $10 고정 (레버리지 3배 고정 = $30 노출)")
        print(f"🌟 대상: {len(symbols)}개 코인 (상위 50개!)")
        print(f"📊 캔들: 15분봉 + 1시간봉 RSI6 분석")
        print(f"⚡ 초고속 스캔: {SCAN_INTERVAL}초 간격 (실시간급!)")
        print(f"🎯 모드: {'자동매매' if AUTO_TRADING_ENABLED else '분석전용'}")
        print(f"🛡️ 리스크: 전봉저가 손절 + 실제 -5% 손절")
        print(f"� 매수: RSI6(15m)30↑ + RSI6(1h)30↑ + RSI6(1h)30근처양봉거래량 + RSI55 50↑ + RSI55→Signal62↑")
        print(f"�💎 매도: RSI6 65↓ + RSI55 49↓ + RSI55→Signal62↓ + 분할익절")
        print(f"🔧 RSI6: 15분봉 기준 정확한 계산")
        print("💡 Ctrl+C로 중지")
        print("=" * 100)

        # 50개 코인 목록 출력 (10개씩)
        print("📊 TOP 50 모니터링 코인:")
        for i, symbol in enumerate(symbols, 1):
            name = symbol.replace('USDT', '')
            print(f"{i:2d}.{name:>6}", end="  ")
            if i % 10 == 0:  # 10개씩 줄바꿈
                print()
        if len(symbols) % 10 != 0:
            print()
        print("=" * 100)

        scan_count = 0

        try:
            while True:
                scan_count += 1

                # 통합 매매 실행
                self.execute_integrated_trading(symbols)

                if mode == 'once':
                    break

                # 초고속 대기
                for i in range(SCAN_INTERVAL, 0, -1):
                    print(f"\r🚀 {i:2d}초 후 다음 초고속 스캔 (50코인)... (Ctrl+C로 중지)", end="", flush=True)
                    time.sleep(1)
                print()

        except KeyboardInterrupt:
            print("\n\n⏹️ 초고속 50코인 시스템 중지")
            active_positions = len([k for k in self.executed_trades.keys() if isinstance(self.executed_trades[k], dict)])
            total_scans = scan_count * len(symbols)
            print(f"🚀 총 {scan_count}회 스캔 ({total_scans:,}개 코인 분석), {active_positions}개 활성 포지션")
            print(f"📊 운영시간: {scan_count * SCAN_INTERVAL / 60:.1f}분 | 평균 {total_scans/(scan_count * SCAN_INTERVAL / 60):.0f} 코인/분")

            # 활성 포지션 요약
            if active_positions > 0:
                print(f"\n💰 현재 활성 포지션:")
                for symbol, info in self.executed_trades.items():
                    if isinstance(info, dict):
                        name = symbol.replace('USDT', '')
                        entry_price = info['entry_price']
                        print(f"   📊 {name}: ${entry_price:.6f} (진입)")

def main():
    """메인 실행 함수"""
    import sys

    # 50개 확장 심볼 사용
    try:
        symbols = TOP_50_SYMBOLS  # 50개 코인 사용
    except:
        symbols = TOP_50_SYMBOLS  # 기본값도 50개

    system = IntegratedTradingSystem()

    print("🚀 초고속 실행 모드 선택:")
    print("1. 초고속 자동매매 (TOP 50코인, 3초 스캔)")
    print("2. 한 번만 분석 + 차트 생성")
    print("3. 분석 전용 (자동매매 비활성화)")

    if len(sys.argv) > 1:
        mode = sys.argv[1]
    else:
        mode = input("모드 선택 (1/2/3): ").strip()

    global AUTO_TRADING_ENABLED

    if mode == "1":
        print("🚀 초고속 자동매매 모드 (TOP 50코인, 3초)")
        AUTO_TRADING_ENABLED = True
        system.run_integrated_system(symbols, 'auto')
    elif mode == "2":
        print("📊 한 번 분석 + 차트 생성")
        AUTO_TRADING_ENABLED = False
        system.run_integrated_system(symbols, 'once')
    elif mode == "3":
        print("📈 분석 전용 모드")
        AUTO_TRADING_ENABLED = False
        system.run_integrated_system(symbols, 'auto')
    else:
        print("❌ 잘못된 선택. 기본 초고속 모드로 실행")
        AUTO_TRADING_ENABLED = True
        system.run_integrated_system(symbols, 'auto')

if __name__ == "__main__":
    main()
