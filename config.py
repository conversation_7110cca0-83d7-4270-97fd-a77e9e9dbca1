# config.py

# 1. Bybit API Credentials
BYBIT_API_KEY = "A1pXxNuW69izQrrinf"
BYBIT_API_SECRET = "KrDbL4GuM3zl4bttuxvNuxy4HSTFN9XIp4GZ"

# 2. Trading Settings
LIVE_TRADING_ENABLED = False  # 실제 거래 활성화 (True로 설정시 주의!)
TRADE_QUANTITY_USDT = 10  # 거래 금액 (USDT)
TRADE_INTERVAL_SECONDS = 60  # 거래 주기 (초)

# 3. Analysis Settings
# 분석할 종목 리스트 (TOP 50 코인)
SYMBOLS = [
    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'ADAUSDT', 'DOGEUSDT', 'AVAXUSDT', 'LINKUSDT',
    'DOTUSDT', 'LTCUSDT', 'BCHUSDT', 'UNIUSDT', 'ATOMUSDT', 'FILUSDT', 'AAVEUSDT', 'SUSHIUSDT',
    'MATICUSDT', 'ALGOUSDT', 'VETUSDT', 'XLMUSDT', 'TRXUSDT', 'ICPUSDT', 'NEARUSDT', 'APTUSDT',
    'OPUSDT', 'ARBUSDT', 'INJUSDT', 'MKRUSDT', 'LDOUSDT', 'STXUSDT', 'IMXUSDT', 'HBARUSDT',
    'CROUSDT', 'RNDRUSDT', 'QNTUSDT', 'GRTUSDT', 'FTMUSDT', 'SANDUSDT', 'MANAUSDT', 'AXSUSDT',
    'THETAUSDT', 'FLOWUSDT', 'CHZUSDT', 'EOSUSDT', 'XTZUSDT', 'KLAYUSDT', 'ARUSDT', 'GMTUSDT',
    'APEUSDT', 'LRCUSDT'
]

# 전체 50개 코인 리스트 (나중에 사용)
SYMBOLS_FULL = [
    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'ADAUSDT', 'DOGEUSDT', 'AVAXUSDT', 'LINKUSDT',
    'DOTUSDT', 'LTCUSDT', 'BCHUSDT', 'UNIUSDT', 'MATICUSDT', 'ATOMUSDT', 'FILUSDT', 'ETCUSDT',
    'XLMUSDT', 'VETUSDT', 'ICPUSDT', 'NEARUSDT', 'ALGOUSDT', 'HBARUSDT', 'QNTUSDT', 'APTUSDT',
    'OPUSDT', 'ARBUSDT', 'MKRUSDT', 'LDOUSDT', 'STXUSDT', 'INJUSDT', 'TIAUSDT', 'SUIUSDT',
    'SEIUSDT', 'RNDRUSDT', 'FETUSDT', 'TAOUSDT', 'ARUSDT', 'WLDUSDT', 'RENDERUSDT', 'GRTUSDT',
    'SANDUSDT', 'MANAUSDT', 'AXSUSDT', 'THETAUSDT', 'FLOWUSDT', 'CHZUSDT', 'ENJUSDT', 'GMTUSDT',
    'GALAUSDT', 'APUSDT'
]

# 실시간 매매용 데이터 설정
INTERVAL = 15  # 15분봉 (실시간 매매 최적화)
YEARS_OF_DATA = 0.1  # 최근 1개월 데이터만 (빠른 분석)

# 분석 기간 (일 단위)
PERIODS = {
    '5일': 5,
    '20일': 20,
    '50일': 50,
    '100일': 100,
    '200일': 200
}

# 기술적 지표 설정
BOLLINGER_SETTINGS = {
    'window': 20,  # 볼린저 밴드 기간
    'std_dev': 2   # 표준편차 배수
}

RSI_SETTINGS = {
    'window': 14  # RSI 계산 기간
}

# 시각화 설정
SAVE_DIR = './analysis_results/'  # 분석 결과 저장 경로
DPI = 300  # 이미지 해상도

# 실시간 통합 시스템 설정
INTEGRATION_SETTINGS = {
    'analysis_results_file': './analysis_results/market_analysis.json',  # 분석 결과 JSON 파일
    'trading_signals_file': './analysis_results/trading_signals.json',   # 매매 신호 파일
    'analysis_cache_file': './analysis_results/analysis_cache.json',     # 분석 캐시 파일 (중복 방지)
    'last_analysis_file': './analysis_results/last_analysis_time.json',  # 마지막 분석 시간
    'top_opportunities_count': 15,  # 상위 기회 종목 수 (증가)
    'auto_trading_enabled': True,   # 자동매매 연동 활성화
    'signal_threshold': 60,         # 신호 강도 임계값 낮춤 (더 많은 기회)
    'realtime_analysis_interval': 30,  # 실시간 분석 주기 (30초)
    'fast_scan_enabled': True,      # 빠른 스캔 모드
    'priority_symbols': ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'ADAUSDT'],  # 우선 분석 종목
    'cache_expiry_minutes': 5       # 캐시 만료 시간 (5분)
}

# 4. Risk Management
STOP_LOSS_PERCENT = 2.0  # 손절라인 (%)
TAKE_PROFIT_PERCENT = 5.0  # 익절라인 (%)

# 5. Notification Settings
TELEGRAM_ENABLED = False
TELEGRAM_BOT_TOKEN = "YOUR_TELEGRAM_BOT_TOKEN"
TELEGRAM_CHAT_ID = "YOUR_TELEGRAM_CHAT_ID"

# config.py에 추가할 설정
# ...

# 6. Advanced Analysis Settings
ADVANCED_ANALYSIS = {
    'volume_cluster_threshold': 0.7,  # 거래량 집중구간 임계값 (전체 거래량 대비 %)
    'divergence_window': 14,         # 다이버전스 검출 기간
    'backtest_period': 90,           # 백테스팅 기간 (일)
    'signal_confirmation': True,     # 신호 확인 사용 여부
}

# config.py

# 7. Backtesting & Optimization Settings
BACKTEST_SETTINGS = {
    'initial_capital': 10000.0,  # 초기 자본금 (USDT)
    'commission_rate': 0.0004,   # 거래 수수료 (0.04%)
    'slippage_rate': 0.0005      # 슬리피지 (0.05%)
}

# 8. Machine Learning Settings (신규 추가)
ML_SETTINGS = {
    'enabled': False,
    'model_path': './models/',
    'retrain_interval': 30  # 모델 재학습 주기 (일)
}

# 9. Sentiment Analysis (신규 추가)
SENTIMENT_SETTINGS = {
    'news_api_key': "YOUR_NEWS_API_KEY",
    'twitter_enabled': False
}