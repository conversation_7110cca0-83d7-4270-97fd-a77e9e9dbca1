import pandas as pd
import numpy as np
import requests
import time
import os
import json
import matplotlib.pyplot as plt
from scipy.stats import linregress
import warnings
from datetime import datetime
import hmac
import hashlib
from config import *  # 설정 파일 임포트

# 경고 무시 설정
warnings.filterwarnings("ignore", category=FutureWarning)
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)  # SSL 경고 무시

# === Bybit API 설정 (인도네시아 지역 최적화) === #
BYBIT_API_KEY = "A1pXxNuW69izQrrinf"
BYBIT_API_SECRET = "KrDbL4GuM3zl4bttuxvNuxy4HSTFN9XIp4GZ"

# 인도네시아에서 접속 가능한 API 엔드포인트들 (우선순위별)
BYBIT_ENDPOINTS = [
    "https://api.bybit.com",           # 기본 글로벌 (다시 시도)
    "https://api.bytick.com",          # 대체 도메인
    "https://api5.bybit.com",          # 아시아 서버
    "https://api-testnet.bybit.com"    # 테스트넷 (최후 수단)
]

BYBIT_BASE_URL = BYBIT_ENDPOINTS[0]  # 작동하는 엔드포인트를 기본값으로

# 디렉토리 생성 함수
def create_directory(path):
    if not os.path.exists(path):
        os.makedirs(path)

# === Bybit API 클래스 (integrated_trading_system.py에서 가져옴) === #
class BybitAPI:
    def __init__(self):
        self.api_key = BYBIT_API_KEY
        self.api_secret = BYBIT_API_SECRET

    def _generate_signature(self, params, timestamp):
        """API 서명 생성"""
        param_str = timestamp + self.api_key + "5000" + params
        return hmac.new(
            bytes(self.api_secret, "utf-8"),
            param_str.encode("utf-8"),
            hashlib.sha256
        ).hexdigest()

    def _make_request(self, endpoint, method="GET", params=None):
        """API 요청 (인도네시아 지역 최적화 - 다중 엔드포인트 시도)"""
        if params is None:
            params = {}

        # 인증이 필요 없는 공개 API의 경우 간단한 요청 사용
        if endpoint == "/v5/market/kline":
            return self._make_public_request(endpoint, params)

        # 인증이 필요한 API의 경우 기존 방식 사용
        timestamp = str(int(time.time() * 1000))

        if method == "GET":
            param_str = "&".join([f"{k}={v}" for k, v in sorted(params.items())])
        else:
            param_str = json.dumps(params) if params else ""

        signature = self._generate_signature(param_str, timestamp)

        headers = {
            "X-BAPI-API-KEY": self.api_key,
            "X-BAPI-SIGN": signature,
            "X-BAPI-SIGN-TYPE": "2",
            "X-BAPI-TIMESTAMP": timestamp,
            "X-BAPI-RECV-WINDOW": "5000",
            "Content-Type": "application/json"
        }

        # 다중 엔드포인트 시도
        for base_url in BYBIT_ENDPOINTS:
            url = f"{base_url}{endpoint}"
            try:
                if method == "GET":
                    response = requests.get(url, params=params, headers=headers, timeout=15, verify=False)
                else:
                    response = requests.post(url, json=params, headers=headers, timeout=15, verify=False)

                if response.status_code == 200:
                    return response.json()

            except Exception as e:
                print(f"[API] {base_url} 연결 실패: {e}")
                continue

        print(f"[API] 모든 엔드포인트 연결 실패")
        return None

    def _make_public_request(self, endpoint, params):
        """공개 API 요청 (인증 불필요)"""
        # 다중 엔드포인트 시도
        for base_url in BYBIT_ENDPOINTS:
            url = f"{base_url}{endpoint}"
            try:
                print(f"     시도 중: {base_url}")
                response = requests.get(url, params=params, timeout=30, verify=False)  # 타임아웃 30초로 증가

                if response.status_code == 200:
                    data = response.json()
                    if data.get('retCode') == 0:
                        print(f"     ✅ 성공: {base_url}")
                        return data
                    else:
                        print(f"     ❌ API 오류: {data.get('retMsg')}")
                else:
                    print(f"     ❌ HTTP 오류: {response.status_code}")

            except Exception as e:
                print(f"     ❌ 연결 오류: {str(e)[:50]}...")
                continue

        return None

# API 인스턴스 생성
bybit_api = BybitAPI()

# 1. 데이터 수집 함수 (개선된 버전)
def fetch_bybit_klines(symbol, interval=INTERVAL, years=YEARS_OF_DATA):
    """Bybit API를 사용해 데이터 수집 (integrated_trading_system.py 방식 적용)"""
    data_list = []
    limit = years * 365 * 24  # 1년치 60분봉 데이터 포인트 수

    end_time = int(time.time() * 1000)
    start_time = end_time - (years * 365 * 24 * 60 * 60 * 1000)

    while start_time < end_time:
        params = {
            "category": "linear",
            "symbol": symbol,
            "interval": str(interval),
            "start": str(start_time),
            "end": str(end_time),
            "limit": str(min(limit, 1000))
        }

        # 새로운 API 클래스 사용
        data = bybit_api._make_request("/v5/market/kline", "GET", params)

        if data and data.get('retCode') == 0:
            chunk = data['result']['list']
            if not chunk:
                break
            data_list.extend(chunk)
            end_time = int(chunk[-1][0]) - 1
            print(f"     {symbol}: {len(chunk)}개 데이터 수집됨")
        else:
            error_msg = data.get('retMsg', 'Unknown error') if data else 'API 요청 실패'
            print(f"     ❌ {symbol} API 오류: {error_msg}")
            break

        time.sleep(0.2)  # API 요청 간격
        limit -= len(chunk)
        if limit <= 0:
            break
    
    if not data_list:
        return pd.DataFrame()
    
    df = pd.DataFrame(data_list, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume', 'turnover'
    ])
    
    df = df[::-1].reset_index(drop=True)
    df['timestamp'] = pd.to_datetime(df['timestamp'].astype('int64'), unit='ms')
    numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'turnover']
    df[numeric_cols] = df[numeric_cols].astype(float)
    df['symbol'] = symbol
    
    return df

# 2. 기술적 지표 계산 함수
def calculate_technical_indicators(df):
    """모든 기술적 지표 계산"""
    # 기본 지표
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
    
    # 볼린저 밴드 추가 (변동성 지표)
    window = BOLLINGER_SETTINGS['window']
    std_dev = BOLLINGER_SETTINGS['std_dev']
    df['ma20'] = df['close'].rolling(window).mean()
    df['std'] = df['close'].rolling(window).std()
    df['upper_band'] = df['ma20'] + (df['std'] * std_dev)
    df['lower_band'] = df['ma20'] - (df['std'] * std_dev)
    
    # 이동평균선 추가
    df['ma50'] = df['close'].rolling(50).mean()
    df['ma100'] = df['close'].rolling(100).mean()
    df['ma200'] = df['close'].rolling(200).mean()
    
    # RSI 추가
    rsi_window = RSI_SETTINGS['window']
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).fillna(0)
    loss = (-delta.where(delta < 0, 0)).fillna(0)
    avg_gain = gain.rolling(rsi_window, min_periods=1).mean()
    avg_loss = loss.rolling(rsi_window, min_periods=1).mean()
    rs = avg_gain / avg_loss.replace(0, np.nan).fillna(0.001)
    df['rsi'] = 100 - (100 / (1 + rs))
    df['rsi'] = df['rsi'].clip(0, 100)
    
    # CVD 추가
    df['delta'] = np.where(df['close'] > df['open'], df['volume'], 
                          np.where(df['close'] < df['open'], -df['volume'], 0))
    df['cvd'] = df['delta'].cumsum()
    
    # OBV 추가
    df['obv'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()
    
    return df

# 3. 고가/저가 및 거래량 분석 함수
def calculate_analysis(df, period_days):
    """주어진 기간에 대한 종합 분석"""
    period_data = df.tail(period_days)
    
    # 고가/저가 계산
    high = period_data['high'].max()
    low = period_data['low'].min()
    current_price = df['close'].iloc[-1]
    high_ratio = (current_price - low) / (high - low) * 100 if high != low else 50
    
    # 지표 값
    ma20 = period_data['ma20'].iloc[-1]
    ma50 = period_data['ma50'].iloc[-1]
    ma100 = period_data['ma100'].iloc[-1] if 'ma100' in period_data.columns else None
    ma200 = period_data['ma200'].iloc[-1] if 'ma200' in period_data.columns else None
    rsi = period_data['rsi'].iloc[-1]
    upper_band = period_data['upper_band'].iloc[-1]
    lower_band = period_data['lower_band'].iloc[-1]
    
    # 거래량 집중구간 분석
    vpvr_bins = 10
    price_range = period_data['high'].max() - period_data['low'].min()
    bin_size = price_range / vpvr_bins
    
    if bin_size > 0:
        bins = [period_data['low'].min() + i * bin_size for i in range(vpvr_bins + 1)]
        period_data = period_data.copy()  # 복사본 생성으로 경고 해결
        period_data['price_bin'] = pd.cut(period_data['typical_price'], bins=bins)
        vpvr = period_data.groupby('price_bin')['volume'].sum().reset_index()
        vpvr['price_mid'] = vpvr['price_bin'].apply(lambda x: x.mid)
        top_volumes = vpvr.nlargest(3, 'volume')
        volume_zones = [{'price': row['price_mid'], 'volume': row['volume']} 
                        for _, row in top_volumes.iterrows()]
    else:
        volume_zones = []
    
    return {
        'high': high,
        'low': low,
        'current_price': current_price,
        'high_ratio': high_ratio,
        'ma20': ma20,
        'ma50': ma50,
        'ma100': ma100,
        'ma200': ma200,
        'rsi': rsi,
        'upper_band': upper_band,
        'lower_band': lower_band,
        'volume_zones': volume_zones
    }

# 4. 시각화 함수 (강화된 기능)
def visualize_analysis(df, symbol, period_name):
    """종목별 분석 결과 시각화"""
    plt.figure(figsize=(18, 16))
    
    # 1. 가격 차트
    plt.subplot(4, 1, 1)
    plt.plot(df['timestamp'], df['close'], label='Price', color='black', alpha=0.8)
    plt.plot(df['timestamp'], df['ma20'], label='MA20', color='blue', alpha=0.7)
    plt.plot(df['timestamp'], df['ma50'], label='MA50', color='orange', alpha=0.7)
    plt.plot(df['timestamp'], df['ma200'], label='MA200', color='red', alpha=0.7)
    
    # 볼린저 밴드 시각화
    plt.fill_between(df['timestamp'], df['upper_band'], df['lower_band'], 
                     color='gray', alpha=0.2, label='Bollinger Bands')
    
    # RSI 과매수/과매도 구간 표시
    plt.title(f'{symbol} {period_name} 분석 - 가격 및 볼린저 밴드')
    plt.legend()
    plt.grid(True)
    
    # 2. RSI 및 볼린저 밴드 폭
    plt.subplot(4, 1, 2)
    plt.plot(df['timestamp'], df['rsi'], label='RSI', color='purple')
    plt.axhline(y=70, color='r', linestyle='--', alpha=0.5)
    plt.axhline(y=30, color='g', linestyle='--', alpha=0.5)
    plt.fill_between(df['timestamp'], 70, 100, color='red', alpha=0.1)
    plt.fill_between(df['timestamp'], 0, 30, color='green', alpha=0.1)
    
    # 볼린저 밴드 폭 (변동성 지표)
    df['bb_width'] = (df['upper_band'] - df['lower_band']) / df['ma20']
    plt.plot(df['timestamp'], df['bb_width'] * 100, label='BB Width (%)', color='brown', alpha=0.7)
    
    plt.title('RSI 및 볼린저 밴드 폭 (변동성)')
    plt.legend()
    plt.grid(True)
    
    # 3. CVD (Cumulative Volume Delta)
    plt.subplot(4, 1, 3)
    plt.plot(df['timestamp'], df['cvd'], label='CVD', color='green')
    plt.plot(df['timestamp'], df['cvd'].rolling(5).mean(), label='CVD MA(5)', linestyle='--', color='darkgreen')
    plt.title('Cumulative Volume Delta (CVD)')
    plt.legend()
    plt.grid(True)
    
    # 4. OBV (On Balance Volume)
    plt.subplot(4, 1, 4)
    plt.plot(df['timestamp'], df['obv'], label='OBV', color='blue')
    plt.title('On Balance Volume (OBV)')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    
    # 파일 저장
    filename = f"{symbol}_{period_name.replace(' ', '')}_analysis.png"
    save_path = os.path.join(SAVE_DIR, filename)
    plt.savefig(save_path, dpi=DPI)
    plt.close()
    
    return save_path

# 5. 트레이딩 전략 생성 함수 (강화된 로직)
def generate_trading_strategy(analysis):
    """분석 데이터 기반 트레이딩 전략 생성"""
    strategies = []
    
    # 기본 조건
    high_ratio = analysis['high_ratio']
    rsi = analysis['rsi']
    current_price = analysis['current_price']
    upper_band = analysis['upper_band']
    lower_band = analysis['lower_band']
    
    # 전략 1: 고가 근접 시나리오
    if high_ratio > 80:
        if rsi < 70:
            strategies.append("고가 근접했으나 RSI가 과매수 수준이 아님 (추가 상승 가능성)")
        else:
            strategies.append("고가 근접 + RSI 과매수 - 매수 신호 고려")
            
        if current_price > upper_band:
            strategies.append("볼린저 밴드 상단 돌파 - 추세 강화 가능성")
    
    # 전략 2: 저가 근접 시나리오
    elif high_ratio < 20:
        if rsi > 30:
            strategies.append("저가 근접했으나 RSI가 과매도 수준이 아님 (추가 하락 가능성)")
        else:
            strategies.append("저가 근접 + RSI 과매도 - 매수 신호 고려")
            
        if current_price < lower_band:
            strategies.append("볼린저 밴드 하단 돌파 - 반등 가능성")
    
    # 전략 3: 중립 구간
    else:
        strategies.append("중립 구간 - 관망 권장")
        
        # 변동성 분석
        bb_width = (analysis['upper_band'] - analysis['lower_band']) / analysis['ma20']
        if bb_width > 0.1:  # 변동성 확대
            strategies.append("변동성 확대 - 브레이크아웃 가능성 대비")
    
    # 거래량 집중구간 기반 전략
    if analysis['volume_zones']:
        main_zone = analysis['volume_zones'][0]['price']
        if current_price < main_zone:
            strategies.append(f"주요 거래량 구간({main_zone:.2f}) 위에 위치 - 돌파 시 강한 저항 예상")
        else:
            strategies.append(f"주요 거래량 구간({main_zone:.2f}) 아래에 위치 - 지지로 작용 가능")
    
    return strategies

# 6. 통합 시스템용 신호 생성 함수
def generate_trading_signals(analysis, symbol):
    """integrated_trading_system.py용 매매 신호 생성"""
    signals = {
        'symbol': symbol,
        'timestamp': datetime.now().isoformat(),
        'signal_strength': 0,  # 0-100 점수
        'recommendation': 'HOLD',  # BUY, SELL, HOLD
        'entry_price': analysis['current_price'],
        'stop_loss': None,
        'take_profit': None,
        'analysis_summary': {}
    }

    # 신호 강도 계산
    score = 0

    # RSI 기반 점수 (30점 만점)
    rsi = analysis['rsi']
    if rsi <= 30:
        score += 30  # 강한 매수 신호
    elif rsi <= 40:
        score += 20  # 중간 매수 신호
    elif rsi >= 70:
        score -= 20  # 매도 신호

    # 고저비율 기반 점수 (25점 만점)
    high_ratio = analysis['high_ratio']
    if high_ratio <= 20:
        score += 25  # 저가 근처
    elif high_ratio <= 40:
        score += 15  # 중하위
    elif high_ratio >= 80:
        score -= 15  # 고가 근처

    # 볼린저 밴드 기반 점수 (25점 만점)
    current_price = analysis['current_price']
    lower_band = analysis['lower_band']
    upper_band = analysis['upper_band']

    if current_price <= lower_band:
        score += 25  # 하단 터치
    elif current_price >= upper_band:
        score -= 15  # 상단 터치

    # 이동평균 기반 점수 (20점 만점)
    ma20 = analysis['ma20']
    ma50 = analysis['ma50']

    if current_price > ma20 > ma50:
        score += 20  # 상승 추세
    elif current_price < ma20 < ma50:
        score -= 10  # 하락 추세

    # 최종 점수 및 추천
    signals['signal_strength'] = max(0, min(100, score))

    if score >= INTEGRATION_SETTINGS['signal_threshold']:
        signals['recommendation'] = 'BUY'
        signals['stop_loss'] = current_price * 0.95  # 5% 손절
        signals['take_profit'] = current_price * 1.10  # 10% 익절
    elif score <= -30:
        signals['recommendation'] = 'SELL'

    # 분석 요약
    signals['analysis_summary'] = {
        'rsi': rsi,
        'high_ratio': high_ratio,
        'price_vs_bb': 'LOWER' if current_price <= lower_band else 'UPPER' if current_price >= upper_band else 'MIDDLE',
        'trend': 'UP' if current_price > ma20 > ma50 else 'DOWN' if current_price < ma20 < ma50 else 'SIDEWAYS'
    }

    return signals

# 7. 통합 결과 저장 함수
def save_integration_results(all_signals):
    """분석 결과를 JSON 파일로 저장하여 integrated_trading_system.py와 연동"""

    # 상위 기회 종목 선별
    buy_signals = [s for s in all_signals if s['recommendation'] == 'BUY']
    buy_signals.sort(key=lambda x: x['signal_strength'], reverse=True)
    top_opportunities = buy_signals[:INTEGRATION_SETTINGS['top_opportunities_count']]

    # 결과 데이터 구성
    integration_data = {
        'analysis_time': datetime.now().isoformat(),
        'total_symbols_analyzed': len(all_signals),
        'buy_signals_count': len(buy_signals),
        'top_opportunities': top_opportunities,
        'all_signals': all_signals
    }

    # JSON 파일로 저장
    os.makedirs(os.path.dirname(INTEGRATION_SETTINGS['analysis_results_file']), exist_ok=True)

    with open(INTEGRATION_SETTINGS['analysis_results_file'], 'w', encoding='utf-8') as f:
        json.dump(integration_data, f, indent=2, ensure_ascii=False)

    # 매매 신호 파일 별도 저장 (integrated_trading_system.py용)
    trading_signals = {
        'timestamp': datetime.now().isoformat(),
        'recommended_symbols': [s['symbol'] for s in top_opportunities],
        'signals': {s['symbol']: s for s in top_opportunities}
    }

    with open(INTEGRATION_SETTINGS['trading_signals_file'], 'w', encoding='utf-8') as f:
        json.dump(trading_signals, f, indent=2, ensure_ascii=False)

    return integration_data

# 8. 메인 실행 함수 (통합 버전)
# 캐시 관리 함수들
def load_analysis_cache():
    """분석 캐시 로드"""
    try:
        if os.path.exists(INTEGRATION_SETTINGS['analysis_cache_file']):
            with open(INTEGRATION_SETTINGS['analysis_cache_file'], 'r', encoding='utf-8') as f:
                return json.load(f)
    except:
        pass
    return {}

def save_analysis_cache(cache_data):
    """분석 캐시 저장"""
    try:
        os.makedirs(os.path.dirname(INTEGRATION_SETTINGS['analysis_cache_file']), exist_ok=True)
        with open(INTEGRATION_SETTINGS['analysis_cache_file'], 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"캐시 저장 오류: {e}")

def is_analysis_needed(symbol, cache_data):
    """분석이 필요한지 확인 (중복 방지)"""
    if symbol not in cache_data:
        return True

    # 캐시 만료 시간 확인
    last_analysis = datetime.fromisoformat(cache_data[symbol]['timestamp'])
    now = datetime.now()
    minutes_passed = (now - last_analysis).total_seconds() / 60

    return minutes_passed >= INTEGRATION_SETTINGS['cache_expiry_minutes']

def analyze_single_batch(symbols_batch, batch_num=1):
    """단일 배치 분석 (중복 방지 + 캐시 활용)"""
    all_signals = []
    successful_analysis = 0

    # 캐시 로드
    cache_data = load_analysis_cache()

    print(f"\n📊 배치 #{batch_num} 분석 중... ({len(symbols_batch)}개 코인)")

    for symbol in symbols_batch:
        try:
            # 중복 분석 방지 체크
            if not is_analysis_needed(symbol, cache_data):
                # 캐시된 데이터 사용
                cached_signal = cache_data[symbol]['signal']
                all_signals.append(cached_signal)
                print(f"  📋 {symbol}: 캐시 사용 ({cached_signal['signal_strength']}/100)")
                successful_analysis += 1
                continue

            # 새로운 분석 수행
            data = fetch_bybit_klines(symbol=symbol, years=0.05)  # 2주 데이터만

            if data.empty:
                continue

            # 기술적 지표 계산
            data = calculate_technical_indicators(data)

            # 5일 기준 빠른 분석
            analysis = calculate_analysis(data, 5)

            # 매매 신호 생성
            signals = generate_trading_signals(analysis, symbol)
            all_signals.append(signals)

            # 캐시에 저장
            cache_data[symbol] = {
                'timestamp': datetime.now().isoformat(),
                'signal': signals
            }

            # 강한 신호만 출력
            if signals['signal_strength'] >= 70:
                print(f"  🔥 {symbol}: {signals['signal_strength']}/100 - {signals['recommendation']} (NEW)")
            else:
                print(f"  ✅ {symbol}: {signals['signal_strength']}/100 (NEW)")

            successful_analysis += 1

        except Exception as e:
            print(f"  ❌ {symbol}: 분석 오류")
            continue

    # 캐시 저장
    save_analysis_cache(cache_data)

    return all_signals, successful_analysis

def main():
    """실시간 통합 분석 시스템 - 빠른 주기 분석"""
    create_directory(SAVE_DIR)

    print(f"\n🚀 실시간 통합 분석 시스템 시작")
    print(f"📊 분석 주기: {INTEGRATION_SETTINGS['realtime_analysis_interval']}초")
    print(f"🎯 분석 종목: {len(SYMBOLS)}개 코인")
    print(f"{'='*80}")

    cycle_count = 0

    while True:
        cycle_count += 1
        start_time = time.time()

        print(f"\n🔄 분석 사이클 #{cycle_count} - {datetime.now().strftime('%H:%M:%S')}")

        all_signals = []
        total_successful = 0

        # 우선순위 종목 먼저 분석
        priority_signals, priority_success = analyze_single_batch(
            INTEGRATION_SETTINGS['priority_symbols'], f"{cycle_count}-P"
        )
        all_signals.extend(priority_signals)
        total_successful += priority_success

        # 나머지 종목 배치별 분석 (10개씩)
        remaining_symbols = [s for s in SYMBOLS if s not in INTEGRATION_SETTINGS['priority_symbols']]
        batch_size = 10

        for i in range(0, len(remaining_symbols), batch_size):
            batch = remaining_symbols[i:i+batch_size]
            batch_signals, batch_success = analyze_single_batch(batch, f"{cycle_count}-{i//batch_size+1}")
            all_signals.extend(batch_signals)
            total_successful += batch_success

        # 분석 결과 저장 및 출력
        if all_signals:
            integration_data = save_integration_results(all_signals)

            # 강한 매수 신호만 출력
            strong_signals = [s for s in all_signals if s['signal_strength'] >= 70]
            if strong_signals:
                print(f"\n🔥 강한 매수 신호 ({len(strong_signals)}개):")
                for signal in strong_signals[:5]:  # 상위 5개만
                    print(f"   {signal['symbol']}: {signal['signal_strength']}/100")

            print(f"\n📊 사이클 완료: {total_successful}/{len(SYMBOLS)}개 성공")
            print(f"💰 매수 신호: {integration_data['buy_signals_count']}개")

        # 다음 분석까지 대기
        elapsed = time.time() - start_time
        wait_time = max(0, INTEGRATION_SETTINGS['realtime_analysis_interval'] - elapsed)

        if wait_time > 0:
            print(f"⏰ {wait_time:.1f}초 후 다음 분석...")
            time.sleep(wait_time)

        # Ctrl+C로 중단 가능
        try:
            pass
        except KeyboardInterrupt:
            print(f"\n🛑 실시간 분석 시스템 중단됨")
            break

    return all_signals
# main6.py에 추가할 기능
# ...

# 7. 고급 분석 기능
def advanced_analysis(df, symbol):
    """고급 분석 기능 추가"""
    # 7.1 다이버전스 검출
    detect_divergence(df, symbol)
    
    # 7.2 거래량 클러스터 심화 분석
    volume_cluster_analysis(df, symbol)
    
    # 7.3 백테스팅 모듈
    backtest_strategy(df, symbol)
    
    # 7.4 리스크 리포트 생성
    generate_risk_report(df, symbol)

# 7.1 다이버전스 검출
def detect_divergence(df, symbol):
    """가격과 지표 간 다이버전스 검출"""
    # RSI 다이버전스
    # OBV 다이버전스
    # MACD 다이버전스
    print(f"{symbol} - 다이버전스 분석 완료")

# 7.2 거래량 클러스터 심화 분석
def volume_cluster_analysis(df, symbol):
    """고급 거래량 클러스터 분석"""
    threshold = ADVANCED_ANALYSIS['volume_cluster_threshold']
    # 고밀도 거래량 구간 식별
    # 지지/저항 수준 강도 계산
    print(f"{symbol} - 거래량 클러스터 분석 완료")

# 7.3 백테스팅 모듈
def backtest_strategy(df, symbol):
    """전략 백테스팅"""
    period = ADVANCED_ANALYSIS['backtest_period']
    # 과거 데이터 기반 전략 테스트
    # 수익률 계산
    # 위험/수익 비율 분석
    print(f"{symbol} - 백테스팅 완료 (최근 {period}일)")

# 7.4 리스크 리포트 생성
def generate_risk_report(df, symbol):
    """리스크 분석 리포트 생성"""
    # 변동성 분석
    # 드로다운 계산
    # 최대 예상 손실(MDD) 평가
    print(f"{symbol} - 리스크 리포트 생성 완료")        

if __name__ == "__main__":
    main()